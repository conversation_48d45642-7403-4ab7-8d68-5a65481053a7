<template>
  <div class="course-library-page">
    <!-- 页面标题和视图切换 -->
    <div class="page-header">
      <div class="header-content">
        <h1>
          <el-icon class="header-icon"><Collection /></el-icon>
          课程库
        </h1>
        <p class="page-description">浏览所有可选课程，制定学习计划</p>
      </div>
      
      <div class="header-actions">
        <el-segmented v-model="viewMode" :options="viewOptions" size="large" />
        <el-button @click="refreshCourses" :loading="isLoading" type="primary">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 高级筛选区域 -->
    <div class="advanced-filters">
      <el-card class="filters-card" shadow="never">
        <div class="filters-header">
          <div class="filters-title">
            <el-icon><Filter /></el-icon>
            <span>高级筛选</span>
            <el-badge 
              v-if="activeFiltersCount > 0" 
              :value="activeFiltersCount" 
              type="primary"
              class="filter-count"
            />
          </div>
          <div class="filters-actions">
            <el-button @click="toggleAdvancedFilters" type="primary" link>
              <el-icon>
                <component :is="showAdvancedFilters ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
              {{ showAdvancedFilters ? '收起' : '展开' }}筛选
            </el-button>
            <el-button 
              @click="resetFilters" 
              type="danger" 
              link
              :disabled="activeFiltersCount === 0"
            >
              <el-icon><RefreshLeft /></el-icon>
              重置筛选
            </el-button>
          </div>
        </div>
        
        <!-- 基础筛选 -->
        <div class="basic-filters">
          <el-row :gutter="24">
            <el-col :xs="24" :sm="8">
              <div class="filter-item">
                <label class="filter-label">搜索课程</label>
                <el-input
                  v-model="searchKeyword"
                  placeholder="课程名称、编号、教师"
                  clearable
                  size="large"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </el-col>
            
            <el-col :xs="24" :sm="8">
              <div class="filter-item">
                <label class="filter-label">课程模块</label>
                <el-select
                  v-model="moduleFilter"
                  placeholder="选择模块"
                  clearable
                  size="large"
                  style="width: 100%"
                >
                  <el-option label="全部模块" value="" />
                  <el-option
                    v-for="module in uniqueModules"
                    :key="module"
                    :label="module"
                    :value="module"
                  />
                </el-select>
              </div>
            </el-col>
            
            <el-col :xs="24" :sm="8">
              <div class="filter-item">
                <label class="filter-label">建议学期</label>
                <el-select
                  v-model="semesterFilter"
                  placeholder="选择学期"
                  clearable
                  size="large"
                  style="width: 100%"
                >
                  <el-option label="全部学期" value="" />
                  <el-option
                    v-for="semester in uniqueSemesters"
                    :key="semester"
                    :label="`第${semester}学期`"
                    :value="semester"
                  />
                </el-select>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 高级筛选 -->
        <transition name="expand">
          <div v-show="showAdvancedFilters" class="advanced-filters-content">
            <el-divider />
            <el-row :gutter="24">
              <el-col :xs="24" :sm="12">
                <div class="filter-item">
                  <label class="filter-label">学分范围</label>
                  <el-slider
                    v-model="creditRange"
                    range
                    :min="0"
                    :max="8"
                    :step="0.5"
                    show-stops
                    :format-tooltip="(val) => `${val}学分`"
                  />
                  <div class="range-display">
                    {{ creditRange[0] }} - {{ creditRange[1] }} 学分
                  </div>
                </div>
              </el-col>
              
              <el-col :xs="24" :sm="12">
                <div class="filter-item">
                  <label class="filter-label">学时范围</label>
                  <el-slider
                    v-model="hourRange"
                    range
                    :min="0"
                    :max="120"
                    :step="8"
                    show-stops
                    :format-tooltip="(val) => `${val}学时`"
                  />
                  <div class="range-display">
                    {{ hourRange[0] }} - {{ hourRange[1] }} 学时
                  </div>
                </div>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :xs="24" :sm="12">
                <div class="filter-item">
                  <label class="filter-label">课程性质</label>
                  <el-checkbox-group v-model="natureFilters">
                    <el-checkbox label="必修" value="必修" />
                    <el-checkbox label="选修" value="选修" />
                    <el-checkbox label="实践" value="实践" />
                  </el-checkbox-group>
                </div>
              </el-col>
              
              <el-col :xs="24" :sm="12">
                <div class="filter-item">
                  <label class="filter-label">排序方式</label>
                  <el-select
                    v-model="sortBy"
                    placeholder="选择排序"
                    size="large"
                    style="width: 100%"
                  >
                    <el-option label="默认排序" value="default" />
                    <el-option label="学分升序" value="credits_asc" />
                    <el-option label="学分降序" value="credits_desc" />
                    <el-option label="学时升序" value="hours_asc" />
                    <el-option label="学时降序" value="hours_desc" />
                    <el-option label="学期升序" value="semester_asc" />
                    <el-option label="学期降序" value="semester_desc" />
                  </el-select>
                </div>
              </el-col>
            </el-row>
          </div>
        </transition>
      </el-card>
    </div>

    <!-- 结果统计和快速操作 -->
    <div class="results-summary">
      <div class="summary-info">
        <el-tag type="info" size="large" effect="light">
          <el-icon><DocumentCopy /></el-icon>
          找到 {{ filteredCourses.length }} 门课程
        </el-tag>
        <div class="quick-stats">
          <span class="stat-item">
            <el-icon><Trophy /></el-icon>
            总计 {{ totalCredits }} 学分
          </span>
          <span class="stat-item">
            <el-icon><Clock /></el-icon>
            总计 {{ totalHours }} 学时
          </span>
        </div>
      </div>
      
      <div class="summary-actions">
        <el-tooltip content="导出课程列表">
          <el-button circle>
            <el-icon><Download /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="批量操作">
          <el-button circle>
            <el-icon><Operation /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 课程内容区域 -->
    <div class="courses-content">
      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'" class="table-view">
        <CourseLibraryTab />
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div v-if="isLoading" class="loading-container">
          <el-row :gutter="24">
            <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="i in 8" :key="i">
              <el-skeleton animated>
                <template #template>
                  <div class="skeleton-card">
                    <el-skeleton-item variant="rect" style="height: 200px;" />
                    <div style="padding: 16px;">
                      <el-skeleton-item variant="h3" style="width: 80%;" />
                      <el-skeleton-item variant="text" style="width: 60%; margin-top: 8px;" />
                      <el-skeleton-item variant="text" style="width: 40%; margin-top: 8px;" />
                    </div>
                  </div>
                </template>
              </el-skeleton>
            </el-col>
          </el-row>
        </div>

        <div v-else-if="filteredCourses.length === 0" class="empty-state">
          <div class="empty-animation">
            <el-icon size="80" color="#cbd5e0"><DocumentRemove /></el-icon>
            <div class="empty-rings">
              <div class="ring ring-1"></div>
              <div class="ring ring-2"></div>
            </div>
          </div>
          <h3>没有找到匹配的课程</h3>
          <p>尝试调整筛选条件或搜索关键词</p>
          <el-button @click="resetFilters" type="primary">
            重置筛选条件
          </el-button>
        </div>

        <div v-else class="course-cards-grid">
          <el-row :gutter="24">
            <el-col 
              :xs="24" :sm="12" :md="8" :lg="6" 
              v-for="(course, index) in paginatedCourses" 
              :key="course.id"
            >
              <div 
                class="course-card" 
                :style="{ animationDelay: `${index * 0.1}s` }"
                @click="viewCourseDetail(course)"
              >
                <div class="card-header">
                  <div class="course-badge">
                    <el-tag 
                      :type="getModuleTagType(course.module)" 
                      effect="dark"
                      size="small"
                    >
                      {{ course.module }}
                    </el-tag>
                  </div>
                  <div class="course-semester">
                    <el-tag 
                      size="small" 
                      :type="getSemesterTagType(course.recommended_semester)"
                      effect="light"
                    >
                      第{{ course.recommended_semester }}学期
                    </el-tag>
                  </div>
                </div>
                
                <div class="card-body">
                  <h3 class="course-title">{{ course.name }}</h3>
                  <div class="course-code">{{ course.code }}</div>
                  
                  <div class="course-meta">
                    <div class="meta-item">
                      <el-icon><Trophy /></el-icon>
                      <span>{{ course.credits }} 学分</span>
                    </div>
                    <div class="meta-item">
                      <el-icon><Clock /></el-icon>
                      <span>{{ course.hours }} 学时</span>
                    </div>
                  </div>
                  
                  <div v-if="course.teacher" class="teacher-info">
                    <el-icon><UserFilled /></el-icon>
                    <span>{{ course.teacher }}</span>
                  </div>
                </div>
                
                <div class="card-footer">
                  <el-button 
                    type="primary" 
                    size="small" 
                    :loading="course.isLoading"
                    @click.stop="toggleCoursePlan(course)"
                  >
                    <el-icon>
                      <component :is="course.isPlanned ? 'RemoveFilled' : 'Plus'" />
                    </el-icon>
                    {{ course.isPlanned ? '移出计划' : '加入计划' }}
                  </el-button>
                  <el-button 
                    size="small" 
                    @click.stop="viewCourseDetail(course)"
                  >
                    查看详情
                  </el-button>
                </div>
                
                <div class="card-decoration"></div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 分页 -->
        <div v-if="filteredCourses.length > 0" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[12, 24, 48, 96]"
            :total="filteredCourses.length"
            layout="total, sizes, prev, pager, next, jumper"
            background
          />
        </div>
      </div>
    </div>

    <!-- 课程详情弹窗 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="selectedCourse?.name"
      width="700px"
      class="course-detail-dialog"
    >
      <div v-if="selectedCourse" class="course-detail">
        <div class="detail-header">
          <div class="detail-title">
            <h2>{{ selectedCourse.name }}</h2>
            <div class="detail-badges">
              <el-tag :type="getModuleTagType(selectedCourse.module)" effect="light">
                {{ selectedCourse.module }}
              </el-tag>
              <el-tag 
                :type="getSemesterTagType(selectedCourse.recommended_semester)"
                effect="light"
              >
                第{{ selectedCourse.recommended_semester }}学期
              </el-tag>
              <el-tag 
                v-if="selectedCourse.isPlanned" 
                type="success" 
                effect="light"
              >
                已在计划中
              </el-tag>
            </div>
          </div>
          <div class="detail-code">{{ selectedCourse.code }}</div>
        </div>
        
        <div class="detail-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="学分">
              <el-tag type="warning" effect="light" size="large">
                {{ selectedCourse.credits }} 学分
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="学时">
              {{ selectedCourse.hours }} 学时
            </el-descriptions-item>
            <el-descriptions-item label="课程模块">
              <el-tag :type="getModuleTagType(selectedCourse.module)" effect="light">
                {{ selectedCourse.module }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="建议学期">
              第{{ selectedCourse.recommended_semester }}学期
            </el-descriptions-item>
            <el-descriptions-item label="任课教师">
              {{ selectedCourse.teacher || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="课程性质">
              {{ selectedCourse.nature || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="先修课程" span="2">
              {{ selectedCourse.prerequisites || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="课程简介" span="2">
              {{ selectedCourse.description || '暂无课程简介' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button 
            @click="toggleCoursePlanInDialog" 
            :type="selectedCourse?.isPlanned ? 'warning' : 'primary'"
            :loading="selectedCourse?.isLoading"
          >
            <el-icon>
              <component :is="selectedCourse?.isPlanned ? 'RemoveFilled' : 'Plus'" />
            </el-icon>
            {{ selectedCourse?.isPlanned ? '移出计划' : '加入计划' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import CourseLibraryTab from '../components/CourseLibraryTab.vue'
import {
  Collection, Refresh, Filter, Search, RefreshLeft, ArrowUp, ArrowDown,
  DocumentCopy, Trophy, Clock, Download, Operation, DocumentRemove,
  UserFilled, Plus, RemoveFilled, View
} from '@element-plus/icons-vue'

// 响应式数据
const viewMode = ref('card')
const showAdvancedFilters = ref(false)
const searchKeyword = ref('')
const moduleFilter = ref('')
const semesterFilter = ref('')
const creditRange = ref([0, 8])
const hourRange = ref([0, 120])
const natureFilters = ref([])
const sortBy = ref('default')
const isLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(24)
const showDetailDialog = ref(false)
const selectedCourse = ref(null)

const viewOptions = [
  { label: '卡片视图', value: 'card' },
  { label: '表格视图', value: 'table' }
]

// 模拟课程数据
const allCourses = ref([
  {
    id: 1, code: 'CS101', name: '计算机科学导论', credits: 3, hours: 48,
    module: '专业基础', teacher: '张教授', recommended_semester: 1,
    nature: '必修', isPlanned: false, isLoading: false,
    prerequisites: '无', description: '本课程是计算机科学专业的入门课程，介绍计算机科学的基本概念。'
  },
  {
    id: 2, code: 'MATH201', name: '高等数学A', credits: 5, hours: 80,
    module: '数学基础', teacher: '李教授', recommended_semester: 1,
    nature: '必修', isPlanned: true, isLoading: false,
    prerequisites: '无', description: '高等数学是理工科学生必修的数学基础课程。'
  },
  {
    id: 3, code: 'CS102', name: '程序设计基础', credits: 4, hours: 64,
    module: '专业核心', teacher: '王教授', recommended_semester: 1,
    nature: '必修', isPlanned: false, isLoading: false,
    prerequisites: '计算机科学导论', description: '学习程序设计的基本方法和技能。'
  },
  {
    id: 4, code: 'CS201', name: '数据结构与算法', credits: 4, hours: 64,
    module: '专业核心', teacher: '赵教授', recommended_semester: 2,
    nature: '必修', isPlanned: false, isLoading: false,
    prerequisites: '程序设计基础', description: '数据结构和算法是程序设计的重要基础。'
  },
  {
    id: 5, code: 'CS301', name: '软件工程', credits: 3, hours: 48,
    module: '专业核心', teacher: '陈教授', recommended_semester: 3,
    nature: '必修', isPlanned: false, isLoading: false,
    prerequisites: '数据结构与算法', description: '学习软件开发的工程化方法和管理。'
  },
  {
    id: 6, code: 'CS302', name: '计算机网络', credits: 3, hours: 48,
    module: '专业核心', teacher: '周教授', recommended_semester: 4,
    nature: '必修', isPlanned: false, isLoading: false,
    prerequisites: '操作系统', description: '学习计算机网络的原理和技术。'
  },
  {
    id: 7, code: 'CS401', name: '人工智能', credits: 3, hours: 48,
    module: '专业选修', teacher: '吴教授', recommended_semester: 5,
    nature: '选修', isPlanned: false, isLoading: false,
    prerequisites: '数据结构与算法', description: '人工智能的基本理论和应用。'
  },
  {
    id: 8, code: 'CS402', name: '机器学习', credits: 3, hours: 48,
    module: '专业选修', teacher: '郑教授', recommended_semester: 6,
    nature: '选修', isPlanned: false, isLoading: false,
    prerequisites: '人工智能', description: '机器学习的算法和实现。'
  }
])

// 计算属性
const filteredCourses = computed(() => {
  let courses = [...allCourses.value]

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    courses = courses.filter(course =>
      course.name.toLowerCase().includes(keyword) ||
      course.code.toLowerCase().includes(keyword) ||
      (course.teacher && course.teacher.toLowerCase().includes(keyword))
    )
  }

  // 模块过滤
  if (moduleFilter.value) {
    courses = courses.filter(course => course.module === moduleFilter.value)
  }

  // 学期过滤
  if (semesterFilter.value) {
    courses = courses.filter(course => course.recommended_semester === semesterFilter.value)
  }

  // 学分范围过滤
  courses = courses.filter(course => 
    course.credits >= creditRange.value[0] && course.credits <= creditRange.value[1]
  )

  // 学时范围过滤
  courses = courses.filter(course => 
    course.hours >= hourRange.value[0] && course.hours <= hourRange.value[1]
  )

  // 课程性质过滤
  if (natureFilters.value.length > 0) {
    courses = courses.filter(course => natureFilters.value.includes(course.nature))
  }

  // 排序
  switch (sortBy.value) {
    case 'credits_asc':
      courses.sort((a, b) => a.credits - b.credits)
      break
    case 'credits_desc':
      courses.sort((a, b) => b.credits - a.credits)
      break
    case 'hours_asc':
      courses.sort((a, b) => a.hours - b.hours)
      break
    case 'hours_desc':
      courses.sort((a, b) => b.hours - a.hours)
      break
    case 'semester_asc':
      courses.sort((a, b) => a.recommended_semester - b.recommended_semester)
      break
    case 'semester_desc':
      courses.sort((a, b) => b.recommended_semester - a.recommended_semester)
      break
  }

  return courses
})

const paginatedCourses = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredCourses.value.slice(start, end)
})

const uniqueModules = computed(() => {
  const modules = [...new Set(allCourses.value.map(course => course.module))]
  return modules.sort()
})

const uniqueSemesters = computed(() => {
  const semesters = [...new Set(allCourses.value.map(course => course.recommended_semester))]
  return semesters.sort((a, b) => a - b)
})

const activeFiltersCount = computed(() => {
  let count = 0
  if (searchKeyword.value) count++
  if (moduleFilter.value) count++
  if (semesterFilter.value) count++
  if (creditRange.value[0] > 0 || creditRange.value[1] < 8) count++
  if (hourRange.value[0] > 0 || hourRange.value[1] < 120) count++
  if (natureFilters.value.length > 0) count++
  if (sortBy.value !== 'default') count++
  return count
})

const totalCredits = computed(() => {
  return filteredCourses.value.reduce((sum, course) => sum + course.credits, 0)
})

const totalHours = computed(() => {
  return filteredCourses.value.reduce((sum, course) => sum + course.hours, 0)
})

// 方法
const refreshCourses = async () => {
  isLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('课程数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败，请重试')
  } finally {
    isLoading.value = false
  }
}

const toggleAdvancedFilters = () => {
  showAdvancedFilters.value = !showAdvancedFilters.value
}

const resetFilters = () => {
  searchKeyword.value = ''
  moduleFilter.value = ''
  semesterFilter.value = ''
  creditRange.value = [0, 8]
  hourRange.value = [0, 120]
  natureFilters.value = []
  sortBy.value = 'default'
  currentPage.value = 1
}

const toggleCoursePlan = async (course) => {
  course.isLoading = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    course.isPlanned = !course.isPlanned
    ElMessage.success(`课程已${course.isPlanned ? '加入' : '移出'}计划`)
  } catch (error) {
    ElMessage.error('操作失败，请重试')
    course.isPlanned = !course.isPlanned
  } finally {
    course.isLoading = false
  }
}

const toggleCoursePlanInDialog = async () => {
  if (selectedCourse.value) {
    await toggleCoursePlan(selectedCourse.value)
  }
}

const viewCourseDetail = (course) => {
  selectedCourse.value = course
  showDetailDialog.value = true
}

const getModuleTagType = (module) => {
  const typeMap = {
    '专业核心': 'danger',
    '专业基础': 'warning',
    '数学基础': 'success',
    '专业选修': 'info',
    '通识教育': 'primary'
  }
  return typeMap[module] || 'info'
}

const getSemesterTagType = (semester) => {
  if (semester <= 2) return 'success'
  if (semester <= 4) return 'warning'
  if (semester <= 6) return 'danger'
  return 'info'
}

// 监听器
watch([searchKeyword, moduleFilter, semesterFilter, creditRange, hourRange, natureFilters, sortBy], () => {
  currentPage.value = 1
})

onMounted(() => {
  refreshCourses()
})
</script>

<style scoped>
.course-library-page {
  padding: 0;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  color: #667eea;
}

.page-description {
  margin: 0;
  color: #718096;
  font-size: 16px;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.advanced-filters {
  margin-bottom: 24px;
}

.filters-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.filters-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
}

.filter-count {
  margin-left: 8px;
}

.filters-actions {
  display: flex;
  gap: 16px;
}

.basic-filters {
  margin-bottom: 0;
}

.filter-item {
  margin-bottom: 16px;
}

.filter-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.range-display {
  text-align: center;
  font-size: 12px;
  color: #718096;
  margin-top: 8px;
}

.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  opacity: 0;
  max-height: 0;
}

.expand-enter-to,
.expand-leave-from {
  opacity: 1;
  max-height: 300px;
}

.results-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.summary-info {
  display: flex;
  align-items: center;
  gap: 24px;
}

.quick-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.summary-actions {
  display: flex;
  gap: 12px;
}

.courses-content {
  margin-bottom: 32px;
}

.loading-container {
  margin-bottom: 32px;
}

.skeleton-card {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.empty-state {
  text-align: center;
  padding: 80px 40px;
  color: #718096;
}

.empty-animation {
  position: relative;
  margin-bottom: 24px;
  display: inline-block;
}

.empty-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring {
  position: absolute;
  border: 2px solid #cbd5e0;
  border-radius: 50%;
  opacity: 0;
  animation: ripple 2s infinite;
}

.ring-1 {
  width: 100px;
  height: 100px;
  margin: -50px 0 0 -50px;
}

.ring-2 {
  width: 120px;
  height: 120px;
  margin: -60px 0 0 -60px;
  animation-delay: 1s;
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.empty-state p {
  margin: 0 0 24px 0;
  font-size: 14px;
}

.course-cards-grid {
  margin-bottom: 32px;
}

.course-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  margin-bottom: 24px;
  animation: cardSlideIn 0.6s ease-out both;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.course-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 20px 0;
  margin-bottom: 16px;
}

.card-body {
  padding: 0 20px;
  margin-bottom: 20px;
}

.course-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  line-height: 1.4;
  min-height: 50px;
}

.course-code {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
  color: #667eea;
  background: #f8fafc;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-bottom: 12px;
  display: inline-block;
}

.course-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #718096;
}

.teacher-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #718096;
}

.card-footer {
  padding: 0 20px 20px;
  display: flex;
  gap: 8px;
}

.card-footer .el-button {
  flex: 1;
}

.card-decoration {
  position: absolute;
  top: -50%;
  right: -20%;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 50%;
  pointer-events: none;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-title {
  flex: 1;
}

.detail-title h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.3;
}

.detail-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.detail-code {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
  color: #667eea;
  background: #f8fafc;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  border: 1px solid #e2e8f0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .header-content h1 {
    font-size: 28px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .filters-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .filters-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .results-summary {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .summary-info {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .quick-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .detail-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .detail-code {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .header-content h1 {
    font-size: 24px;
  }
  
  .course-card {
    margin-bottom: 16px;
  }
  
  .card-footer {
    flex-direction: column;
    gap: 8px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .course-library-page {
    color: #f7fafc;
  }
  
  .filters-card,
  .course-card {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .course-code,
  .detail-code {
    background: #374151;
    color: #93c5fd;
  }
  
  .results-summary {
    background: linear-gradient(135deg, #2d3748 0%, #374151 100%);
    border-color: #4a5568;
  }
}

/* 滑块样式增强 */
:deep(.el-slider__runway) {
  background-color: #e2e8f0;
  border-radius: 6px;
}

:deep(.el-slider__bar) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 6px;
}

:deep(.el-slider__button) {
  border: 2px solid #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 对话框样式增强 */
:deep(.course-detail-dialog .el-dialog) {
  border-radius: 12px;
}

:deep(.course-detail-dialog .el-dialog__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px 12px 0 0;
  border-bottom: 1px solid #e5e7eb;
}
</style> 