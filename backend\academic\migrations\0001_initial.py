# Generated by Django 5.2.3 on 2025-06-25 10:05

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Course',
            fields=[
                ('course_code', models.CharField(max_length=20, primary_key=True, serialize=False, verbose_name='课程代码')),
                ('name', models.CharField(max_length=100, verbose_name='课程名称')),
                ('english_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='英文名称')),
                ('credits', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0)], verbose_name='学分')),
                ('credit_category', models.CharField(choices=[('通识必修', '通识教育必修'), ('通识选修', '通识教育选修'), ('学科基础', '学科基础课程'), ('专业必修', '专业必修课程'), ('专业选修-模块', '专业方向模块选修'), ('专业选修-任选', '专业任选课程'), ('实践环节', '集中实践环节'), ('创新创业', '课外创新创业'), ('素质拓展', '素质拓展')], max_length=50, verbose_name='学分归类')),
                ('module_name', models.CharField(choices=[('无', '不属于任何模块'), ('企业应用', '企业应用软件方向'), ('智能软件', '智能软件方向'), ('云数据', '云数据服务方向'), ('开源应用', '开源应用软件方向')], default='无', max_length=50, verbose_name='所属专业模块')),
                ('total_hours', models.IntegerField(validators=[django.core.validators.MinValueValidator(0)], verbose_name='总学时')),
                ('theory_hours', models.IntegerField(validators=[django.core.validators.MinValueValidator(0)], verbose_name='理论学时')),
                ('practice_hours', models.IntegerField(validators=[django.core.validators.MinValueValidator(0)], verbose_name='实践学时')),
                ('recommended_semester', models.IntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(8)], verbose_name='建议开课学期')),
            ],
        ),
        migrations.CreateModel(
            name='StudentProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='StudentCourseRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('completed', '已修完'), ('taking', '在读中'), ('planned', '计划修')], default='planned', max_length=20, verbose_name='修读状态')),
                ('score', models.FloatField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='最终成绩')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_records', to='academic.course')),
                ('student_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='course_records', to='academic.studentprofile')),
            ],
            options={
                'unique_together': {('student_profile', 'course')},
            },
        ),
    ]
