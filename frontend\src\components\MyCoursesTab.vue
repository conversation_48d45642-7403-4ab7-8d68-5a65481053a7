<template>
  <div class="my-courses-container">
    <!-- 顶部统计信息 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :xs="12" :sm="6">
          <div class="stat-item">
            <div class="stat-value">{{ totalCourses }}</div>
            <div class="stat-label">总课程数</div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-item completed">
            <div class="stat-value">{{ completedCourses }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-item taking">
            <div class="stat-value">{{ takingCourses }}</div>
            <div class="stat-label">在读中</div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-item planned">
            <div class="stat-value">{{ plannedCourses }}</div>
            <div class="stat-label">计划修</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 操作栏 -->
    <div class="actions-bar">
      <div class="left-actions">
        <el-button 
          type="primary" 
          @click="saveChanges" 
          :disabled="!isChanged"
          :loading="isSaving"
        >
          <el-icon><Upload /></el-icon>
          保存更改 {{ changedCourses.size > 0 ? `(${changedCourses.size})` : '' }}
        </el-button>
        
        <el-button @click="batchMarkCompleted" :disabled="selectedRows.length === 0">
          <el-icon><Check /></el-icon>
          批量标记为已完成
        </el-button>
        
        <el-button @click="clearSelection">
          <el-icon><Close /></el-icon>
          清除选择
        </el-button>
      </div>
      
      <div class="right-actions">
        <el-input
          v-model="searchQuery"
          placeholder="搜索课程..."
          clearable
          style="width: 200px;"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select v-model="statusFilter" placeholder="状态筛选" clearable style="width: 140px;">
          <el-option label="全部" value="" />
          <el-option label="已完成" value="completed" />
          <el-option label="在读中" value="taking" />
          <el-option label="计划修" value="planned" />
        </el-select>
        
        <el-select v-model="categoryFilter" placeholder="分类筛选" clearable style="width: 160px;">
          <el-option label="全部分类" value="" />
          <el-option v-for="category in categories" :key="category" :label="category" :value="category" />
        </el-select>
      </div>
    </div>

    <!-- 提示信息 -->
    <el-alert 
      v-if="isChanged" 
      title="有未保存的更改" 
      type="info" 
      show-icon 
      :closable="false" 
      class="change-alert"
    >
      <template #default>
        您已修改了 {{ changedCourses.size }} 门课程的状态，请记得保存更改。
      </template>
    </el-alert>

    <!-- 课程表格 -->
    <el-table 
      ref="tableRef"
      :data="filteredCourses" 
      v-loading="isLoading"
      @selection-change="handleSelectionChange"
      height="500px"
      stripe
      border
      class="courses-table"
    >
      <el-table-column type="selection" width="50" />
      
      <el-table-column label="状态" width="120" align="center">
        <template #default="scope">
          <el-select 
            v-model="scope.row.status"
            @change="markAsChanged(scope.row.course_code)"
            size="small"
            style="width: 100px;"
          >
            <el-option label="计划修" value="planned">
              <div class="status-option planned">
                <el-icon><Clock /></el-icon>
                计划修
              </div>
            </el-option>
            <el-option label="在读中" value="taking">
              <div class="status-option taking">
                <el-icon><Reading /></el-icon>
                在读中
              </div>
            </el-option>
            <el-option label="已完成" value="completed">
              <div class="status-option completed">
                <el-icon><CircleCheckFilled /></el-icon>
                已完成
              </div>
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      
      <el-table-column prop="course_code" label="课程代码" width="120" />
      
      <el-table-column prop="name" label="课程名称" min-width="200">
        <template #default="scope">
          <div class="course-name">
            <span class="name">{{ scope.row.name }}</span>
            <el-tag 
              v-if="scope.row.english_name" 
              size="small" 
              type="info" 
              class="english-name"
            >
              {{ scope.row.english_name }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="credits" label="学分" width="80" align="center">
        <template #default="scope">
          <el-tag :type="getCreditTagType(scope.row.credits)" size="small">
            {{ scope.row.credits }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="credit_category" label="学分归类" width="140">
        <template #default="scope">
          <el-tag :color="getCategoryColor(scope.row.credit_category)" size="small">
            {{ scope.row.credit_category }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="recommended_semester" label="建议学期" width="100" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.recommended_semester" type="warning" size="small">
            第{{ scope.row.recommended_semester }}学期
          </el-tag>
          <span v-else class="not-specified">-</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="total_hours" label="总学时" width="90" align="center" />
      
      <el-table-column label="操作" width="100" align="center" fixed="right">
        <template #default="scope">
          <el-button 
            v-if="scope.row.status !== 'completed'"
            type="success" 
            size="small" 
            @click="quickComplete(scope.row)"
            link
          >
            <el-icon><Check /></el-icon>
            完成
          </el-button>
          <el-button 
            v-else
            type="info" 
            size="small" 
            disabled
            link
          >
            <el-icon><CircleCheckFilled /></el-icon>
            已完成
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :disabled="false"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="filteredCourses.length"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import axios from 'axios'
import { useProgressStore } from '../store/progress'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Upload, Check, Close, Search, Clock, Reading, CircleCheckFilled
} from '@element-plus/icons-vue'

const progressStore = useProgressStore()
const tableRef = ref()

const allCourses = ref<any[]>([])
const isLoading = ref(true)
const isSaving = ref(false)
const changedCourses = ref(new Set())
const selectedRows = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(20)

// 筛选条件
const searchQuery = ref('')
const statusFilter = ref('')
const categoryFilter = ref('')

const isChanged = computed(() => changedCourses.value.size > 0)

// 统计数据
const totalCourses = computed(() => allCourses.value.length)
const completedCourses = computed(() => allCourses.value.filter(c => c.status === 'completed').length)
const takingCourses = computed(() => allCourses.value.filter(c => c.status === 'taking').length)
const plannedCourses = computed(() => allCourses.value.filter(c => c.status === 'planned').length)

// 获取所有课程分类
const categories = computed(() => {
  const cats = new Set(allCourses.value.map(c => c.credit_category))
  return Array.from(cats).sort()
})

// 筛选后的课程
const filteredCourses = computed(() => {
  let filtered = allCourses.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(course => 
      course.name.toLowerCase().includes(query) ||
      course.course_code.toLowerCase().includes(query) ||
      (course.english_name && course.english_name.toLowerCase().includes(query))
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(course => course.status === statusFilter.value)
  }

  if (categoryFilter.value) {
    filtered = filtered.filter(course => course.credit_category === categoryFilter.value)
  }

  return filtered
})

const markAsChanged = (courseCode: string) => {
  changedCourses.value.add(courseCode)
}

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

const clearSelection = () => {
  tableRef.value?.clearSelection()
}

const quickComplete = (course: any) => {
  course.status = 'completed'
  markAsChanged(course.course_code)
  ElMessage.success(`${course.name} 已标记为完成`)
}

const batchMarkCompleted = async () => {
  if (selectedRows.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要将选中的 ${selectedRows.value.length} 门课程标记为已完成吗？`,
      '批量操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    selectedRows.value.forEach(course => {
      course.status = 'completed'
      markAsChanged(course.course_code)
    })
    
    ElMessage.success(`已将 ${selectedRows.value.length} 门课程标记为完成`)
    clearSelection()
  } catch {
    // 用户取消操作
  }
}

onMounted(async () => {
  try {
    const [coursesRes, recordsRes] = await Promise.all([
      axios.get('/api/courses/'),
      axios.get('/api/me/records/')
    ])

    const myRecords = new Map(recordsRes.data.map((r: any) => [r.course, r.status]))
    
    allCourses.value = coursesRes.data.map((c: any) => ({
      ...c,
      status: myRecords.get(c.course_code) || 'planned'
    }))

  } catch (error) {
    console.error("Failed to load course data:", error)
    ElMessage.error('加载课程数据失败')
  } finally {
    isLoading.value = false
  }
})

const saveChanges = async () => {
  if (changedCourses.value.size === 0) return
  
  isSaving.value = true
  
  try {
    const payload = Array.from(changedCourses.value).map(courseCode => {
      const course = allCourses.value.find(c => c.course_code === courseCode)
      return {
        course_code: course.course_code,
        status: course.status
      }
    })

    await axios.post('/api/me/records/', payload)
    await progressStore.fetchAuditReport()
    
    changedCourses.value.clear()
    ElMessage.success(`成功保存 ${payload.length} 门课程的状态`)
  } catch (error) {
    console.error("Failed to save changes:", error)
    ElMessage.error('保存失败，请重试')
  } finally {
    isSaving.value = false
  }
}

const getCreditTagType = (credits: number) => {
  if (credits >= 4) return 'danger'
  if (credits >= 2) return 'warning'
  return 'success'
}

const getCategoryColor = (category: string) => {
  const colors = {
    '通识必修': '#409EFF',
    '通识选修': '#67C23A', 
    '学科基础': '#E6A23C',
    '专业必修': '#F56C6C',
    '专业选修-模块': '#909399',
    '专业选修-任选': '#909399',
    '实践环节': '#722ED1',
    '创新创业': '#13C2C2',
    '素质拓展': '#52C41A'
  }
  return colors[category] || '#909399'
}
</script>

<style scoped>
.my-courses-container {
  padding: 0;
}

.stats-section {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-item.completed .stat-value {
  color: #67C23A;
}

.stat-item.taking .stat-value {
  color: #E6A23C;
}

.stat-item.planned .stat-value {
  color: #909399;
}

.stat-label {
  font-size: 12px;
  color: #606266;
  margin-top: 4px;
}

.actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.left-actions, .right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.change-alert {
  margin-bottom: 16px;
  border-radius: 8px;
}

.courses-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.course-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.name {
  font-weight: 500;
}

.english-name {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-option {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-option.completed {
  color: #67C23A;
}

.status-option.taking {
  color: #E6A23C;
}

.status-option.planned {
  color: #909399;
}

.not-specified {
  color: #C0C4CC;
  font-style: italic;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .actions-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .left-actions, .right-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  :deep(.el-table) {
    font-size: 12px;
  }
  
  :deep(.el-table .cell) {
    padding: 4px 8px;
  }
}

/* 表格样式增强 */
:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table tr:hover > td) {
  background-color: #f0f9ff;
}

:deep(.el-table .el-table__body tr.current-row > td) {
  background-color: #e6f7ff;
}
</style> 