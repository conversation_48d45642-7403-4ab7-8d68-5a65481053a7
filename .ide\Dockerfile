# 1. 以官方的 Node.js 22 镜像为基础
FROM node:22

# 2. 安装 Python 3、pip，以及创建虚拟环境所需的 venv 模块
RUN apt-get update && \
    apt-get install -y python3 python3-pip python3-venv --no-install-recommends && \
    rm -rf /var/lib/apt/lists/*

# 3. 安装 code-server (WebIDE 核心服务) 和 SSH 服务
RUN apt-get update && \
    apt-get install -y openssh-server --no-install-recommends && \
    rm -rf /var/lib/apt/lists/* && \
    curl -fsSL https://code-server.dev/install.sh | sh

# 4. 安装 Python 和 Vue.js 开发所需的核心 VS Code 插件
RUN code-server --install-extension ms-python.python \
  && code-server --install-extension Vue.volar \
  && code-server --install-extension alexcvzz.vscode-sqlite \
  && code-server --install-extension dbaeumer.vscode-eslint \
  && code-server --install-extension esbenp.prettier-vscode \
  && echo "Python and Vue.js extensions installed successfully."

# 5. 设置字符集，以支持在终端中显示中文
ENV LANG C.UTF-8
ENV LANGUAGE C.UTF-8