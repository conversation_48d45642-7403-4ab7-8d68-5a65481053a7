<template>
  <div class="course-library-container">
    <!-- 筛选和搜索区域 -->
    <div class="filter-section">
      <el-card class="filter-card" shadow="never">
        <template #header>
          <div class="filter-header">
            <el-icon><Filter /></el-icon>
            <span>课程筛选</span>
            <el-button text @click="resetFilters" class="reset-btn">
              <el-icon><RefreshRight /></el-icon>
              重置筛选
            </el-button>
          </div>
        </template>
        
        <el-row :gutter="16" class="filter-row">
          <el-col :xs="24" :sm="12" :md="6">
            <el-input
              v-model="searchQuery"
              placeholder="搜索课程名或代码..."
              clearable
              @keyup.enter="fetchCourses"
              @clear="fetchCourses"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="6">
            <el-select
              v-model="selectedCategory"
              placeholder="课程分类"
              clearable
              @change="fetchCourses"
            >
              <el-option
                v-for="item in creditCategories"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <div class="category-option">
                  <span class="category-dot" :style="{backgroundColor: getCategoryColor(item.value)}"></span>
                  {{ item.label }}
                </div>
              </el-option>
            </el-select>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="6">
            <el-select
              v-model="selectedSemester"
              placeholder="建议学期"
              clearable
              @change="fetchCourses"
            >
              <el-option v-for="sem in 8" :key="sem" :label="`第 ${sem} 学期`" :value="sem" />
            </el-select>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="6">
            <el-button @click="fetchCourses" type="primary" style="width: 100%;">
              <el-icon><Search /></el-icon>
              查询课程
            </el-button>
          </el-col>
        </el-row>

        <!-- 高级筛选 -->
        <el-collapse v-model="advancedVisible" class="advanced-filters">
          <el-collapse-item name="advanced">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>高级筛选</span>
            </template>
            <el-row :gutter="16">
              <el-col :xs="24" :sm="8">
                <div class="filter-item">
                  <label>学分范围</label>
                  <el-slider
                    v-model="creditRange"
                    range
                    :min="0"
                    :max="6"
                    :step="0.5"
                    show-stops
                    @change="fetchCourses"
                  />
                  <div class="range-labels">
                    <span>{{ creditRange[0] }}</span>
                    <span>{{ creditRange[1] }}</span>
                  </div>
                </div>
              </el-col>
              <el-col :xs="24" :sm="8">
                <div class="filter-item">
                  <label>总学时范围</label>
                  <el-slider
                    v-model="hoursRange"
                    range
                    :min="0"
                    :max="200"
                    :step="10"
                    show-stops
                    @change="fetchCourses"
                  />
                  <div class="range-labels">
                    <span>{{ hoursRange[0] }}h</span>
                    <span>{{ hoursRange[1] }}h</span>
                  </div>
                </div>
              </el-col>
              <el-col :xs="24" :sm="8">
                <div class="filter-item">
                  <label>专业模块</label>
                  <el-select v-model="selectedModule" placeholder="选择模块" clearable @change="fetchCourses">
                    <el-option label="无" value="无" />
                    <el-option label="企业应用" value="企业应用" />
                    <el-option label="智能软件" value="智能软件" />
                    <el-option label="云数据" value="云数据" />
                    <el-option label="开源应用" value="开源应用" />
                  </el-select>
                </div>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </el-card>
    </div>

    <!-- 结果统计 -->
    <div class="results-header">
      <div class="results-info">
        <el-icon><DataBoard /></el-icon>
        <span>共找到 <strong>{{ courses.length }}</strong> 门课程</span>
        <el-divider direction="vertical" />
        <span>总学分: <strong>{{ totalCredits }}</strong></span>
      </div>
      
      <div class="view-controls">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="table">
            <el-icon><List /></el-icon>
            列表
          </el-radio-button>
          <el-radio-button label="card">
            <el-icon><Grid /></el-icon>
            卡片
          </el-radio-button>
        </el-radio-group>
        
        <el-select v-model="sortBy" style="width: 140px; margin-left: 12px;" @change="applySorting">
          <el-option label="课程代码" value="course_code" />
          <el-option label="课程名称" value="name" />
          <el-option label="学分" value="credits" />
          <el-option label="总学时" value="total_hours" />
          <el-option label="建议学期" value="recommended_semester" />
        </el-select>
        
        <el-button @click="toggleSortOrder" :icon="sortOrder === 'asc' ? 'sort-up' : 'sort-down'" circle size="small" />
      </div>
    </div>

    <!-- 课程列表 - 表格视图 -->
    <el-table 
      v-if="viewMode === 'table'"
      :data="paginatedCourses" 
      v-loading="loading" 
      stripe 
      border
      class="courses-table"
      @row-click="showCourseDetail"
    >
      <el-table-column prop="course_code" label="课程代码" width="120" />
      
      <el-table-column prop="name" label="课程名称" min-width="200">
        <template #default="scope">
          <div class="course-name-cell">
            <div class="course-title">{{ scope.row.name }}</div>
            <div v-if="scope.row.english_name" class="english-name">{{ scope.row.english_name }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="credits" label="学分" width="80" align="center">
        <template #default="scope">
          <el-tag :type="getCreditTagType(scope.row.credits)" size="small">
            {{ scope.row.credits }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="credit_category" label="学分归类" width="140">
        <template #default="scope">
          <el-tag :color="getCategoryColor(scope.row.credit_category)" size="small">
            {{ scope.row.credit_category }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="recommended_semester" label="建议学期" width="100" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.recommended_semester" type="warning" size="small">
            第{{ scope.row.recommended_semester }}学期
          </el-tag>
          <span v-else class="not-specified">-</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="total_hours" label="总学时" width="90" align="center" />
      
      <el-table-column label="学时分配" width="120" align="center">
        <template #default="scope">
          <el-tooltip content="理论学时 / 实践学时" placement="top">
            <div class="hours-breakdown">
              <span class="theory-hours">{{ scope.row.theory_hours }}</span>
              <el-divider direction="vertical" />
              <span class="practice-hours">{{ scope.row.practice_hours }}</span>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="80" align="center" fixed="right">
        <template #default="scope">
          <el-button @click.stop="showCourseDetail(scope.row)" type="primary" link size="small">
            <el-icon><View /></el-icon>
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 课程列表 - 卡片视图 -->
    <div v-else class="courses-grid" v-loading="loading">
      <el-row :gutter="16">
        <el-col 
          v-for="course in paginatedCourses" 
          :key="course.course_code"
          :xs="24" :sm="12" :md="8" :lg="6"
          class="course-card-col"
        >
          <el-card class="course-card" shadow="hover" @click="showCourseDetail(course)">
            <div class="card-header">
              <div class="course-code">{{ course.course_code }}</div>
              <el-tag :type="getCreditTagType(course.credits)" size="small">
                {{ course.credits }}学分
              </el-tag>
            </div>
            
            <div class="card-content">
              <h4 class="course-title">{{ course.name }}</h4>
              <p v-if="course.english_name" class="english-subtitle">{{ course.english_name }}</p>
              
              <div class="course-meta">
                <div class="meta-item">
                  <el-icon><Clock /></el-icon>
                  <span>{{ course.total_hours }}学时</span>
                </div>
                <div class="meta-item" v-if="course.recommended_semester">
                  <el-icon><Calendar /></el-icon>
                  <span>第{{ course.recommended_semester }}学期</span>
                </div>
              </div>
              
              <div class="course-category">
                <el-tag :color="getCategoryColor(course.credit_category)" size="small">
                  {{ course.credit_category }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 48, 96]"
        :small="false"
        :disabled="false"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="courses.length"
      />
    </div>

    <!-- 课程详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      :title="selectedCourse?.name"
      width="600px"
      class="course-detail-dialog"
    >
      <div v-if="selectedCourse" class="course-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="课程代码">{{ selectedCourse.course_code }}</el-descriptions-item>
          <el-descriptions-item label="学分">
            <el-tag :type="getCreditTagType(selectedCourse.credits)">{{ selectedCourse.credits }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="英文名称" :span="2">
            {{ selectedCourse.english_name || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="学分归类">
            <el-tag :color="getCategoryColor(selectedCourse.credit_category)">
              {{ selectedCourse.credit_category }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="专业模块">{{ selectedCourse.module_name }}</el-descriptions-item>
          <el-descriptions-item label="建议学期">
            {{ selectedCourse.recommended_semester ? `第${selectedCourse.recommended_semester}学期` : '不限' }}
          </el-descriptions-item>
          <el-descriptions-item label="总学时">{{ selectedCourse.total_hours }}</el-descriptions-item>
          <el-descriptions-item label="理论学时">{{ selectedCourse.theory_hours }}</el-descriptions-item>
          <el-descriptions-item label="实践学时">{{ selectedCourse.practice_hours }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';
import { ElMessage } from 'element-plus';
import { 
  Filter, RefreshRight, Search, Setting, DataBoard, List, Grid, 
  View, Clock, Calendar
} from '@element-plus/icons-vue';

const searchQuery = ref('');
const selectedCategory = ref('');
const selectedSemester = ref<number | ''>('');
const selectedModule = ref('');
const courses = ref([]);
const loading = ref(false);
const viewMode = ref('table');
const sortBy = ref('course_code');
const sortOrder = ref('asc');
const currentPage = ref(1);
const pageSize = ref(24);
const advancedVisible = ref([]);
const creditRange = ref([0, 6]);
const hoursRange = ref([0, 200]);
const detailVisible = ref(false);
const selectedCourse = ref(null);

const creditCategories = ref([
  { value: '通识必修', label: '通识教育必修' },
  { value: '通识选修', label: '通识教育选修' },
  { value: '学科基础', label: '学科基础课程' },
  { value: '专业必修', label: '专业必修课程' },
  { value: '专业选修-模块', label: '专业方向模块选修' },
  { value: '专业选修-任选', label: '专业任选课程' },
  { value: '实践环节', label: '集中实践环节' },
  { value: '创新创业', label: '课外创新创业' },
  { value: '素质拓展', label: '素质拓展' },
]);

const totalCredits = computed(() => {
  return courses.value.reduce((sum, course) => sum + course.credits, 0).toFixed(1);
});

const paginatedCourses = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return courses.value.slice(start, end);
});

const fetchCourses = async () => {
  loading.value = true;
  try {
    const params: any = {};
    if (searchQuery.value) params.search = searchQuery.value;
    if (selectedCategory.value) params.credit_category = selectedCategory.value;
    if (selectedSemester.value) params.recommended_semester = selectedSemester.value;

    const response = await axios.get('/api/courses/', { params });
    let result = response.data;

    // 应用高级筛选
    if (selectedModule.value) {
      result = result.filter(course => course.module_name === selectedModule.value);
    }
    
    result = result.filter(course => 
      course.credits >= creditRange.value[0] && 
      course.credits <= creditRange.value[1] &&
      course.total_hours >= hoursRange.value[0] && 
      course.total_hours <= hoursRange.value[1]
    );

    courses.value = result;
    applySorting();
  } catch (error) {
    console.error('Failed to fetch courses:', error);
    ElMessage.error('获取课程库失败，请稍后再试。');
  } finally {
    loading.value = false;
  }
};

const applySorting = () => {
  courses.value.sort((a, b) => {
    let aValue = a[sortBy.value];
    let bValue = b[sortBy.value];
    
    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }
    
    if (sortOrder.value === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
};

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  applySorting();
};

const resetFilters = () => {
  searchQuery.value = '';
  selectedCategory.value = '';
  selectedSemester.value = '';
  selectedModule.value = '';
  creditRange.value = [0, 6];
  hoursRange.value = [0, 200];
  fetchCourses();
};

const showCourseDetail = (course: any) => {
  selectedCourse.value = course;
  detailVisible.value = true;
};

const getCreditTagType = (credits: number) => {
  if (credits >= 4) return 'danger';
  if (credits >= 2) return 'warning';
  return 'success';
};

const getCategoryColor = (category: string) => {
  const colors = {
    '通识必修': '#409EFF',
    '通识选修': '#67C23A', 
    '学科基础': '#E6A23C',
    '专业必修': '#F56C6C',
    '专业选修-模块': '#909399',
    '专业选修-任选': '#909399',
    '实践环节': '#722ED1',
    '创新创业': '#13C2C2',
    '素质拓展': '#52C41A'
  };
  return colors[category] || '#909399';
};

onMounted(() => {
  fetchCourses();
});
</script>

<style scoped>
.course-library-container {
  padding: 0;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  border-radius: 8px;
}

.filter-header {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: space-between;
}

.reset-btn {
  font-size: 12px;
}

.filter-row {
  margin-bottom: 16px;
}

.category-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.advanced-filters {
  margin-top: 16px;
}

.filter-item {
  margin-bottom: 16px;
}

.filter-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
  font-size: 13px;
}

.range-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.results-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
}

.view-controls {
  display: flex;
  align-items: center;
}

.courses-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.course-name-cell {
  line-height: 1.4;
}

.course-title {
  font-weight: 500;
  margin-bottom: 2px;
}

.english-name {
  font-size: 12px;
  color: #909399;
}

.hours-breakdown {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.theory-hours {
  color: #409EFF;
}

.practice-hours {
  color: #67C23A;
}

.not-specified {
  color: #C0C4CC;
  font-style: italic;
}

.courses-grid {
  margin-bottom: 20px;
}

.course-card-col {
  margin-bottom: 16px;
}

.course-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 200px;
  border-radius: 8px;
}

.course-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.course-code {
  font-family: 'Monaco', 'Menlo', monospace;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}

.card-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.english-subtitle {
  font-size: 12px;
  color: #909399;
  margin: 0 0 12px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.course-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.course-category {
  margin-top: auto;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.course-detail {
  padding: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .results-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .view-controls {
    justify-content: center;
  }
  
  .course-card {
    height: auto;
    min-height: 180px;
  }
  
  .filter-row .el-col {
    margin-bottom: 12px;
  }
}

/* 表格样式增强 */
:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table tbody tr:hover > td) {
  background-color: #f0f9ff;
  cursor: pointer;
}

:deep(.el-collapse-item__header) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}
</style> 