import { ref, watch, readonly } from 'vue'

export type Theme = 'light' | 'dark' | 'auto'

const THEME_KEY = 'courseflow-theme'

export function useTheme() {
  const theme = ref<Theme>((localStorage.getItem(THEME_KEY) as Theme) || 'light')
  const systemTheme = ref<'light' | 'dark'>('light')
  
  // 检测系统主题
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  systemTheme.value = mediaQuery.matches ? 'dark' : 'light'
  
  // 监听系统主题变化
  mediaQuery.addEventListener('change', (e) => {
    systemTheme.value = e.matches ? 'dark' : 'light'
    applyTheme()
  })
  
  // 应用主题
  const applyTheme = () => {
    const root = document.documentElement
    let actualTheme: 'light' | 'dark'
    
    if (theme.value === 'auto') {
      actualTheme = systemTheme.value
    } else {
      actualTheme = theme.value
    }
    
    root.setAttribute('data-theme', actualTheme)
    root.className = actualTheme
  }
  
  // 设置主题
  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme
    localStorage.setItem(THEME_KEY, newTheme)
    applyTheme()
  }
  
  // 切换主题
  const toggleTheme = () => {
    const newTheme = theme.value === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }
  
  // 获取当前实际主题
  const getCurrentTheme = (): 'light' | 'dark' => {
    if (theme.value === 'auto') {
      return systemTheme.value
    }
    return theme.value
  }
  
  // 监听主题变化
  watch(theme, applyTheme, { immediate: true })
  
  return {
    theme: readonly(theme),
    systemTheme: readonly(systemTheme),
    setTheme,
    toggleTheme,
    getCurrentTheme,
  }
}
