<template>
  <div class="dashboard">
    <!-- 背景装饰 -->
    <div class="dashboard-bg">
      <div class="bg-gradient"></div>
      <div class="bg-pattern"></div>
    </div>

    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="welcome-section">
          <h1 class="welcome-title">
            <span class="gradient-text">欢迎回来</span>
            <el-icon class="wave-icon"><WindPower /></el-icon>
          </h1>
          <p class="welcome-subtitle">{{ getWelcomeMessage() }}</p>
        </div>
        
        <div class="header-actions">
          <el-button 
            @click="refreshData" 
            :loading="progressStore.isLoading" 
            type="primary"
            size="large"
            class="refresh-btn"
          >
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <LoadingOverlay 
      :visible="progressStore.isLoading && !hasInitialData" 
      text="正在加载学分数据..."
    />

    <!-- 主要内容 -->
    <div v-if="progressStore.auditReport" class="dashboard-content">
      <!-- 统计卡片 -->
      <div class="stats-section">
        <div class="section-header">
          <h2 class="section-title">学分统计概览</h2>
          <p class="section-subtitle">实时追踪您的学习进度</p>
        </div>
        
        <div class="stats-grid">
          <GlassCard 
            v-for="(stat, index) in statsData" 
            :key="stat.key"
            :variant="stat.variant"
            hoverable
            class="stat-card animate__animated animate__fadeInUp"
            :style="{ animationDelay: `${index * 0.1}s` }"
          >
            <div class="stat-content">
              <div class="stat-icon-wrapper" :class="stat.variant">
                <el-icon :size="32">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              
              <div class="stat-info">
                <div class="stat-number">
                  <AnimatedNumber 
                    :value="stat.value" 
                    :duration="1200"
                    :suffix="stat.suffix"
                  />
                </div>
                <div class="stat-label">{{ stat.label }}</div>
                <div v-if="stat.description" class="stat-description">
                  {{ stat.description }}
                </div>
              </div>
              
              <div v-if="stat.progress !== undefined" class="stat-progress">
                <el-progress 
                  :percentage="stat.progress"
                  :stroke-width="8"
                  :show-text="false"
                  :color="getProgressColor(stat.progress)"
                  class="progress-bar"
                />
                <div class="progress-text">{{ stat.progress }}%</div>
              </div>
            </div>
          </GlassCard>
        </div>
      </div>

      <!-- 详细信息和图表 -->
      <div class="details-section">
        <div class="details-grid">
          <!-- 学分分布图表 -->
          <GlassCard class="chart-card">
            <template #header>
              <div class="card-header">
                <div class="header-info">
                  <el-icon class="header-icon"><PieChart /></el-icon>
                  <div>
                    <h3>学分分布</h3>
                    <p>各模块完成情况详细分析</p>
                  </div>
                </div>
                <el-button type="primary" link @click="$router.push('/audit')">
                  查看详情 <el-icon><ArrowRight /></el-icon>
                </el-button>
              </div>
            </template>
            
            <div class="chart-container">
              <div id="creditChart" class="chart"></div>
            </div>
          </GlassCard>

          <!-- 最近动态 -->
          <GlassCard class="activity-card">
            <template #header>
              <div class="card-header">
                <div class="header-info">
                  <el-icon class="header-icon"><Clock /></el-icon>
                  <div>
                    <h3>最近动态</h3>
                    <p>您的学习轨迹</p>
                  </div>
                </div>
              </div>
            </template>
            
            <div class="activity-list">
              <div 
                v-for="(activity, index) in recentActivities" 
                :key="index"
                class="activity-item"
              >
                <div class="activity-icon" :class="activity.type">
                  <el-icon>
                    <component :is="activity.icon" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-description">{{ activity.description }}</div>
                  <div class="activity-time">{{ activity.time }}</div>
                </div>
              </div>
            </div>
          </GlassCard>
        </div>

        <!-- 课程进度详情 -->
        <GlassCard class="progress-card">
          <template #header>
            <div class="card-header">
              <div class="header-info">
                <el-icon class="header-icon"><Grid /></el-icon>
                <div>
                  <h3>各模块学分详情</h3>
                  <p>详细的学分分类完成情况</p>
                </div>
              </div>
            </div>
          </template>
          
          <div class="progress-list">
            <div
              v-for="(category, index) in progressStore.auditReport.category_details"
              :key="category.name"
              class="progress-item"
              :style="{ animationDelay: `${index * 0.1}s` }"
            >
              <div class="progress-header">
                <div class="category-info">
                  <span class="category-name">{{ category.name }}</span>
                  <span class="category-status" :class="getCategoryStatus(category)">
                    {{ getCategoryStatusText(category) }}
                  </span>
                </div>
                <div class="category-credits">
                  <AnimatedNumber :value="category.completed" />
                  <span class="credits-separator">/</span>
                  <span class="credits-total">{{ category.required }}</span>
                  <span class="credits-unit">学分</span>
                </div>
              </div>
              
              <div class="progress-bar-container">
                <el-progress 
                  :percentage="getProgressPercentage(category.completed, category.required)"
                  :stroke-width="8"
                  :show-text="false"
                  :color="getProgressColor(getProgressPercentage(category.completed, category.required))"
                />
              </div>
              
              <div v-if="category.courses && category.courses.length > 0" class="course-list">
                <div class="course-header">
                  <span>已完成课程</span>
                  <el-tag size="small" type="info">{{ category.courses.length }} 门</el-tag>
                </div>
                <div class="course-tags">
                  <el-tag 
                    v-for="course in category.courses.slice(0, 5)" 
                    :key="course.id"
                    size="small"
                    class="course-tag"
                  >
                    {{ course.name }}
                  </el-tag>
                  <el-tag 
                    v-if="category.courses.length > 5"
                    size="small"
                    type="info"
                  >
                    +{{ category.courses.length - 5 }} 门
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </GlassCard>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!progressStore.isLoading" class="empty-state">
      <div class="empty-content">
        <el-icon class="empty-icon" size="80"><DocumentRemove /></el-icon>
        <h3>暂无数据</h3>
        <p>请先刷新数据或联系管理员</p>
        <el-button type="primary" @click="refreshData">
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useProgressStore } from '../store/progress'
import { useUserStore } from '../store/user'
import * as echarts from 'echarts'
import GlassCard from '../components/GlassCard.vue'
import AnimatedNumber from '../components/AnimatedNumber.vue'
import LoadingOverlay from '../components/LoadingOverlay.vue'

const progressStore = useProgressStore()
const userStore = useUserStore()

const hasInitialData = ref(false)

// 统计数据
const statsData = computed(() => {
  const report = progressStore.auditReport
  if (!report) return []

  const totalCompleted = report.category_details.reduce((sum, cat) => sum + cat.completed, 0)
  const totalRequired = report.category_details.reduce((sum, cat) => sum + cat.required, 0)
  const completionRate = Math.round((totalCompleted / totalRequired) * 100) || 0
  
  return [
    {
      key: 'total',
      label: '总学分',
      value: totalCompleted,
      suffix: ` / ${totalRequired}`,
      description: '已完成学分',
      progress: completionRate,
      icon: 'Trophy',
      variant: 'primary'
    },
    {
      key: 'completed',
      label: '已完成',
      value: totalCompleted,
      suffix: ' 学分',
      description: '累计获得',
      icon: 'CircleCheckFilled',
      variant: 'success'
    },
    {
      key: 'remaining',
      label: '剩余学分',
      value: Math.max(0, totalRequired - totalCompleted),
      suffix: ' 学分',
      description: '还需完成',
      icon: 'Clock',
      variant: 'warning'
    },
    {
      key: 'progress',
      label: '完成度',
      value: completionRate,
      suffix: '%',
      description: '整体进度',
      icon: 'TrendCharts',
      variant: 'default'
    }
  ]
})

// 最近动态数据
const recentActivities = computed(() => [
  {
    type: 'success',
    icon: 'CircleCheckFilled',
    title: '课程完成',
    description: '《软件工程基础》课程已完成',
    time: '2 小时前'
  },
  {
    type: 'primary',
    icon: 'DocumentAdd',
    title: '数据更新',
    description: '学分数据已同步更新',
    time: '1 天前'
  },
  {
    type: 'warning',
    icon: 'WarningFilled',
    title: '注意提醒',
    description: '部分课程学分待确认',
    time: '3 天前'
  }
])

// 工具函数
const getWelcomeMessage = () => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好！开始新的一天学习吧 🌅'
  if (hour < 18) return '下午好！继续努力完成学业目标 ☀️'
  return '晚上好！回顾今天的学习成果 🌙'
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#10b981'
  if (percentage >= 60) return '#3b82f6'
  if (percentage >= 40) return '#f59e0b'
  return '#ef4444'
}

const getProgressPercentage = (completed: number, required: number) => {
  return Math.round((completed / required) * 100) || 0
}

const getCategoryStatus = (category: any) => {
  const percentage = getProgressPercentage(category.completed, category.required)
  if (percentage >= 100) return 'completed'
  if (percentage >= 80) return 'almost'
  if (percentage >= 50) return 'progress'
  return 'start'
}

const getCategoryStatusText = (category: any) => {
  const percentage = getProgressPercentage(category.completed, category.required)
  if (percentage >= 100) return '已完成'
  if (percentage >= 80) return '即将完成'
  if (percentage >= 50) return '进行中'
  return '刚开始'
}

const refreshData = async () => {
  await progressStore.fetchAuditReport()
  hasInitialData.value = true
  await nextTick()
  initChart()
}

// 初始化图表
const initChart = () => {
  const chartDom = document.getElementById('creditChart')
  if (!chartDom || !progressStore.auditReport) return

  const myChart = echarts.init(chartDom)
  const data = progressStore.auditReport.category_details.map(cat => ({
    name: cat.name,
    value: cat.completed,
    required: cat.required
  }))

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} 学分 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        color: 'var(--text-primary)'
      }
    },
    series: [
      {
        name: '学分分布',
        type: 'pie',
        radius: '50%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        }
      }
    ]
  }

  myChart.setOption(option)
  
  // 响应式调整
  window.addEventListener('resize', () => myChart.resize())
}

onMounted(async () => {
  if (progressStore.auditReport) {
    hasInitialData.value = true
    await nextTick()
    initChart()
  } else {
    await refreshData()
  }
})
</script>

<style scoped>
.dashboard {
  min-height: calc(100vh - 144px);
  position: relative;
  overflow: hidden;
}

.dashboard-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1) 0%, 
    rgba(16, 185, 129, 0.05) 50%, 
    transparent 100%);
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  background-size: 400px 400px;
  animation: float 20s ease-in-out infinite;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.welcome-section {
  flex: 1;
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-color), var(--success-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.wave-icon {
  color: var(--warning-color);
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.welcome-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.refresh-btn {
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.stats-section {
  margin-bottom: 1rem;
}

.section-header {
  text-align: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.section-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  padding: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-content {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stat-icon-wrapper {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
}

.stat-icon-wrapper.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.stat-icon-wrapper.success {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
}

.stat-icon-wrapper.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 8px 24px rgba(245, 158, 11, 0.3);
}

.stat-icon-wrapper.default {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  box-shadow: 0 8px 24px rgba(107, 114, 128, 0.3);
}

.stat-icon-wrapper .el-icon {
  color: white;
  z-index: 1;
}

.stat-icon-wrapper::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.stat-description {
  font-size: 0.875rem;
  color: var(--text-tertiary);
}

.stat-progress {
  margin-top: 1rem;
}

.progress-bar {
  margin-bottom: 0.5rem;
}

.progress-text {
  text-align: right;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.details-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.chart-card,
.activity-card,
.progress-card {
  min-height: 400px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.header-info h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.header-info p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.chart-container {
  height: 300px;
  width: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.7);
  transform: translateX(4px);
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

.activity-icon.success {
  background: var(--success-color);
}

.activity-icon.primary {
  background: var(--primary-color);
}

.activity-icon.warning {
  background: var(--warning-color);
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.activity-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.progress-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.progress-item {
  padding: 1.5rem;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.progress-item:hover {
  background: rgba(255, 255, 255, 0.7);
  transform: translateY(-2px);
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.category-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.category-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-status.completed {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success-color);
}

.category-status.almost {
  background: rgba(59, 130, 246, 0.2);
  color: var(--primary-color);
}

.category-status.progress {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning-color);
}

.category-status.start {
  background: rgba(107, 114, 128, 0.2);
  color: var(--text-tertiary);
}

.category-credits {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.credits-separator {
  color: var(--text-tertiary);
  margin: 0 0.25rem;
}

.credits-total {
  color: var(--text-secondary);
}

.credits-unit {
  font-size: 0.875rem;
  color: var(--text-tertiary);
  margin-left: 0.25rem;
}

.progress-bar-container {
  margin-bottom: 1rem;
}

.course-list {
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.course-header {
  display: flex;
  align-items: center;
  justify-content: between;
  gap: 1rem;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.course-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.course-tag {
  font-size: 0.75rem;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
  padding: 2rem;
}

.empty-icon {
  color: var(--text-tertiary);
  margin-bottom: 1rem;
}

.empty-content h3 {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.empty-content p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .welcome-title {
    font-size: 2rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .course-header {
    flex-direction: column;
    align-items: flex-start;
  }
}
                    <div class="category-details">
                      <span class="credits-info">{{ category.completed }} / {{ category.required }} 学分</span>
                    </div>
                  </div>
                  <el-tag 
                    :type="getTagType(category.completed, category.required)"
                    size="large"
                    effect="light"
                    class="status-tag"
                  >
                    {{ category.completed >= category.required ? '已完成' : '进行中' }}
                  </el-tag>
                </div>
                
                <div class="category-progress">
                  <el-progress
                    :percentage="calculatePercentage(category.completed, category.required)"
                    :status="getProgressStatus(category.completed, category.required)"
                    :stroke-width="8"
                    striped
                    striped-flow
                    class="animated-progress"
                  />
                </div>
                
                <div v-if="category.details && category.details.length > 0" class="category-courses">
                  <div class="courses-summary">
                    <el-icon><Document /></el-icon>
                    <span>已完成 {{ category.details.length }} 门课程</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <el-col :xs="24" :lg="8">
          <div class="chart-card warnings-card">
            <div class="card-header">
              <div class="header-left">
                <el-icon class="header-icon" :color="warningIconColor">
                  <component :is="warningIcon" />
                </el-icon>
                <div>
                  <h3>毕业预警</h3>
                  <p>实时监控毕业要求</p>
                </div>
              </div>
              <el-badge 
                v-if="progressStore.auditReport.warnings.length > 0" 
                :value="progressStore.auditReport.warnings.length" 
                type="warning"
                class="warning-count"
              />
            </div>
            
            <div class="warnings-content">
              <div v-if="progressStore.auditReport.warnings.length === 0" class="no-warnings">
                <div class="success-animation">
                  <el-icon size="64" color="#10b981"><CircleCheckFilled /></el-icon>
                  <div class="success-rings">
                    <div class="ring ring-1"></div>
                    <div class="ring ring-2"></div>
                    <div class="ring ring-3"></div>
                  </div>
                </div>
                <h4>恭喜您！</h4>
                <p>目前没有预警项目，您的学分进度良好</p>
              </div>
              <div v-else class="warnings-list">
                <div
                  v-for="(warning, index) in progressStore.auditReport.warnings.slice(0, 3)"
                  :key="index"
                  class="warning-item"
                  :style="{ animationDelay: `${index * 0.1}s` }"
                >
                  <el-alert
                    :title="warning"
                    type="warning"
                    show-icon
                    :closable="false"
                    effect="light"
                  />
                </div>
                <div v-if="progressStore.auditReport.warnings.length > 3" class="more-warnings">
                  <el-button type="warning" link @click="$router.push('/audit')">
                    还有 {{ progressStore.auditReport.warnings.length - 3 }} 项预警，点击查看全部
                    <el-icon><ArrowRight /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 快速操作区域 -->
      <div class="quick-actions-section">
        <div class="section-title">
          <h2>快速操作</h2>
          <p>选择您需要的功能模块</p>
        </div>
        
        <el-row :gutter="24">
          <el-col :xs="24" :sm="8" v-for="(action, index) in quickActions" :key="action.route">
            <div 
              class="action-card" 
              @click="$router.push(action.route)"
              :style="{ animationDelay: `${index * 0.1}s` }"
            >
              <div class="action-icon" :style="{ background: action.gradient }">
                <el-icon size="32">
                  <component :is="action.icon" />
                </el-icon>
              </div>
              <div class="action-content">
                <h3>{{ action.title }}</h3>
                <p>{{ action.description }}</p>
              </div>
              <div class="action-arrow">
                <el-icon><ArrowRight /></el-icon>
              </div>
              <div class="action-decoration"></div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useProgressStore } from '../store/progress'
import { 
  Loading, Trophy, Check, TrendCharts, WarningFilled, CircleCheckFilled,
  PieChart, Warning, ArrowRight, User, Collection, Document, Odometer, Refresh
} from '@element-plus/icons-vue'

// 简单的数字动画组件
const AnimatedNumber = {
  props: ['value'],
  template: '<span>{{ displayValue }}</span>',
  data() {
    return {
      displayValue: 0
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.animateToValue(newVal)
      },
      immediate: true
    }
  },
  methods: {
    animateToValue(target) {
      const duration = 1000
      const startTime = Date.now()
      const startValue = this.displayValue
      
      const animate = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)
        
        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4)
        this.displayValue = Math.round(startValue + (target - startValue) * easeOutQuart)
        
        if (progress < 1) {
          requestAnimationFrame(animate)
        }
      }
      
      animate()
    }
  }
}

const progressStore = useProgressStore()

const completedCoursesCount = computed(() => {
  if (!progressStore.auditReport?.category_details) return 0
  return progressStore.auditReport.category_details.reduce(
    (total, category) => total + (category.details?.length || 0), 0
  )
})

const statCards = computed(() => [
  {
    key: 'total-credits',
    label: '总学分',
    value: progressStore.auditReport?.total_credits_summary.completed || 0,
    total: progressStore.auditReport?.total_credits_summary.required || 151,
    progress: calculatePercentage(
      progressStore.auditReport?.total_credits_summary.completed || 0,
      progressStore.auditReport?.total_credits_summary.required || 151
    ),
    icon: Trophy,
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: '#667eea',
    class: 'total-credits'
  },
  {
    key: 'completed-courses',
    label: '已完成课程',
    value: completedCoursesCount.value,
    icon: Check,
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    color: '#4facfe',
    class: 'completed-courses'
  },
  {
    key: 'completion-rate',
    label: '完成度',
    value: Math.round(calculatePercentage(
      progressStore.auditReport?.total_credits_summary.completed || 0,
      progressStore.auditReport?.total_credits_summary.required || 151
    )),
    icon: TrendCharts,
    gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    color: '#fa709a',
    class: 'completion-rate'
  },
  {
    key: 'warnings',
    label: '预警项目',
    value: progressStore.auditReport?.warnings.length || 0,
    icon: progressStore.auditReport?.warnings.length > 0 ? WarningFilled : CircleCheckFilled,
    gradient: progressStore.auditReport?.warnings.length > 0 
      ? 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'
      : 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    color: progressStore.auditReport?.warnings.length > 0 ? '#ff9a9e' : '#a8edea',
    class: 'warnings'
  }
])

const warningIcon = computed(() => {
  return progressStore.auditReport?.warnings.length > 0 ? WarningFilled : CircleCheckFilled
})

const warningIconColor = computed(() => {
  return progressStore.auditReport?.warnings.length > 0 ? '#f59e0b' : '#10b981'
})

const quickActions = [
  {
    title: '管理我的课程',
    description: '标记已完成的课程，更新学习进度',
    icon: User,
    route: '/my-courses',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    title: '浏览课程库',
    description: '查看所有可选课程，制定学习计划',
    icon: Collection,
    route: '/course-library',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
  },
  {
    title: '毕业审核',
    description: '详细查看毕业要求完成情况',
    icon: Document,
    route: '/audit',
    gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
  }
]

onMounted(() => {
  progressStore.fetchAuditReport()
})

const calculatePercentage = (completed: number, required: number) => {
  if (required === 0) return 100
  return Math.min((completed / required) * 100, 100)
}

const getProgressStatus = (completed: number, required: number) => {
  if (required === 0) return 'success'
  const percentage = (completed / required) * 100
  if (percentage >= 100) return 'success'
  if (percentage >= 80) return undefined
  return 'exception'
}

const getTagType = (completed: number, required: number) => {
  if (completed >= required) return 'success'
  if (completed >= required * 0.8) return 'warning'
  return 'danger'
}
</script>

<style scoped>
.dashboard-page {
  padding: 0;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  color: #667eea;
}

.page-description {
  margin: 0;
  color: #718096;
  font-size: 16px;
  line-height: 1.5;
}

.header-actions {
  flex-shrink: 0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 24px;
}

.loading-spinner {
  font-size: 48px;
  color: #667eea;
}

.loading-text {
  font-size: 18px;
  color: #718096;
  font-weight: 500;
}

.loading-skeleton {
  width: 100%;
  max-width: 600px;
}

.dashboard-content {
  animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-title {
  margin-bottom: 32px;
  text-align: center;
}

.section-title h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
}

.section-title p {
  margin: 0;
  color: #718096;
  font-size: 16px;
}

.overview-section {
  margin-bottom: 48px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  animation: cardSlideIn 0.6s ease-out both;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-unit {
  font-size: 18px;
  font-weight: 500;
  color: #718096;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-progress {
  position: relative;
}

.progress-bar {
  border-radius: 8px;
  overflow: hidden;
}

.stat-decoration {
  position: absolute;
  top: -50%;
  right: -20%;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 50%;
  pointer-events: none;
}

.charts-section {
  margin-bottom: 48px;
}

.chart-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 24px;
  position: relative;
  overflow: hidden;
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f7fafc;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left .header-icon {
  font-size: 24px;
  color: #667eea;
}

.header-left h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
}

.header-left p {
  margin: 0;
  font-size: 14px;
  color: #718096;
}

.category-progress-list {
  display: grid;
  gap: 24px;
}

.category-item {
  padding: 24px;
  background: #f7fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  animation: categorySlideIn 0.6s ease-out both;
}

@keyframes categorySlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.category-item:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateX(4px);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.category-info {
  flex: 1;
}

.category-name {
  font-weight: 600;
  font-size: 16px;
  color: #1a202c;
  display: block;
  margin-bottom: 4px;
}

.category-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.credits-info {
  font-size: 14px;
  color: #718096;
}

.status-tag {
  border-radius: 8px;
  font-weight: 500;
}

.category-progress {
  margin-bottom: 16px;
}

.animated-progress {
  border-radius: 8px;
}

.category-courses {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.courses-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #718096;
}

.warnings-card {
  background: linear-gradient(135deg, #fff 0%, #f7fafc 100%);
}

.warning-count {
  margin-left: 8px;
}

.warnings-content {
  min-height: 280px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.no-warnings {
  text-align: center;
  padding: 40px 20px;
  position: relative;
}

.success-animation {
  position: relative;
  margin-bottom: 24px;
  display: inline-block;
}

.success-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring {
  position: absolute;
  border: 2px solid #10b981;
  border-radius: 50%;
  opacity: 0;
  animation: ripple 2s infinite;
}

.ring-1 {
  width: 80px;
  height: 80px;
  margin: -40px 0 0 -40px;
}

.ring-2 {
  width: 100px;
  height: 100px;
  margin: -50px 0 0 -50px;
  animation-delay: 0.5s;
}

.ring-3 {
  width: 120px;
  height: 120px;
  margin: -60px 0 0 -60px;
  animation-delay: 1s;
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.no-warnings h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.no-warnings p {
  margin: 0;
  color: #718096;
}

.warnings-list {
  display: grid;
  gap: 12px;
}

.warning-item {
  animation: warningSlideIn 0.6s ease-out both;
}

@keyframes warningSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.more-warnings {
  text-align: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #fed7aa;
}

.quick-actions-section {
  margin-bottom: 32px;
}

.action-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  height: 200px;
  animation: actionSlideIn 0.6s ease-out both;
}

@keyframes actionSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.action-card:hover .action-arrow {
  transform: translateX(4px);
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72px;
  height: 72px;
  border-radius: 20px;
  color: white;
  margin-bottom: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.action-content {
  flex: 1;
  margin-bottom: 16px;
}

.action-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.action-content p {
  margin: 0;
  color: #718096;
  font-size: 14px;
  line-height: 1.5;
}

.action-arrow {
  color: #718096;
  transition: transform 0.3s ease;
}

.action-decoration {
  position: absolute;
  top: -50%;
  right: -20%;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 50%;
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-content h1 {
    font-size: 28px;
  }
  
  .stat-card {
    margin-bottom: 16px;
    padding: 20px;
  }
  
  .stat-content {
    margin-bottom: 16px;
  }
  
  .stat-icon {
    width: 56px;
    height: 56px;
  }
  
  .stat-number {
    font-size: 24px;
  }
  
  .chart-card {
    padding: 24px;
  }
  
  .action-card {
    height: 180px;
    padding: 24px;
  }
  
  .action-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .header-content h1 {
    font-size: 24px;
  }
  
  .section-title h2 {
    font-size: 20px;
  }
  
  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .dashboard-page {
    color: #f7fafc;
  }
  
  .stat-card,
  .chart-card,
  .action-card {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .category-item {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .category-item:hover {
    background: #374151;
  }
}
</style> 