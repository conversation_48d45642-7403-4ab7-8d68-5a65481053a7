from django.contrib import admin
from .models import Course, StudentProfile, StudentCourseRecord

@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ('course_code', 'name', 'credits', 'credit_category', 'recommended_semester')
    search_fields = ('name', 'course_code')
    list_filter = ('credit_category', 'recommended_semester', 'module_name')

class StudentCourseRecordInline(admin.TabularInline):
    model = StudentCourseRecord
    extra = 1 # 默认显示一条空的记录以便添加

@admin.register(StudentProfile)
class StudentProfileAdmin(admin.ModelAdmin):
    inlines = [StudentCourseRecordInline]
    list_display = ('user',)
    search_fields = ('user__username',)

@admin.register(StudentCourseRecord)
class StudentCourseRecordAdmin(admin.ModelAdmin):
    list_display = ('student_profile', 'course', 'status', 'score')
    list_filter = ('status',)
    search_fields = ('student_profile__user__username', 'course__name')
