import { defineStore } from 'pinia'
import axios from 'axios'
import router from '../router'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || null,
    userInfo: null as any,
  }),
  getters: {
    isAuthenticated: (state) => !!state.token,
  },
  actions: {
    async login(credentials: any) {
      try {
        const response = await axios.post('/api/auth/login/', credentials)
        const token = response.data.access
        this.token = token
        localStorage.setItem('token', token)
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
        await this.fetchUserInfo()
        router.push('/')
      } catch (error) {
        console.error('Login failed:', error)
        // 可以增加错误处理逻辑，例如弹窗提示
      }
    },
    logout() {
      this.token = null
      this.userInfo = null
      localStorage.removeItem('token')
      delete axios.defaults.headers.common['Authorization']
      router.push('/login')
    },
    async fetchUserInfo() {
        if (!this.isAuthenticated) return;
        // 注意：我们的后端示例没有一个专门获取用户信息的 /api/me/ 接口
        // 我们暂时将登录时的用户信息存储起来，或者您可以创建一个
        // 这里只是一个占位符
    },
    // 初始化时检查 token
    init() {
        if (this.token) {
            axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`
        }
    }
  },
}) 