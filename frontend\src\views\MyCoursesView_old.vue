<template>
  <div class="my-courses-page">
    <!-- 页面标题和快速统计 -->
    <div class="page-header">
      <div class="header-content">
        <h1>
          <el-icon class="header-icon"><User /></el-icon>
          我的课程
        </h1>
        <p class="page-description">管理您的课程进度，标记已完成的课程</p>
      </div>
      
      <!-- 快速统计卡片 -->
      <div class="quick-stats">
        <div class="stat-card mini" v-for="(stat, index) in quickStats" :key="stat.key" 
             :style="{ animationDelay: `${index * 0.1}s` }">
          <div class="stat-icon" :style="{ background: stat.gradient }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索区域 -->
    <div class="filters-section">
      <el-card class="filters-card" shadow="never">
        <div class="filters-header">
          <div class="filters-title">
            <el-icon><Filter /></el-icon>
            <span>筛选和搜索</span>
          </div>
          <el-button 
            @click="resetFilters" 
            type="primary" 
            link
            :disabled="!hasActiveFilters"
          >
            <el-icon><RefreshLeft /></el-icon>
            重置筛选
          </el-button>
        </div>
        
        <div class="filters-content">
          <el-row :gutter="24">
            <el-col :xs="24" :sm="8">
              <div class="filter-item">
                <label class="filter-label">搜索课程</label>
                <el-input
                  v-model="searchKeyword"
                  placeholder="输入课程名称、编号或教师"
                  clearable
                  size="large"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </el-col>
            
            <el-col :xs="24" :sm="8">
              <div class="filter-item">
                <label class="filter-label">完成状态</label>
                <el-select
                  v-model="statusFilter"
                  placeholder="选择状态"
                  clearable
                  size="large"
                  style="width: 100%"
                >
                  <el-option label="全部" value="" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="未完成" value="not_completed" />
                </el-select>
              </div>
            </el-col>
            
            <el-col :xs="24" :sm="8">
              <div class="filter-item">
                <label class="filter-label">课程模块</label>
                <el-select
                  v-model="moduleFilter"
                  placeholder="选择模块"
                  clearable
                  size="large"
                  style="width: 100%"
                >
                  <el-option label="全部" value="" />
                  <el-option
                    v-for="module in uniqueModules"
                    :key="module"
                    :label="module"
                    :value="module"
                  />
                </el-select>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 批量操作工具栏 -->
    <div v-if="selectedCourses.length > 0" class="batch-actions">
      <div class="batch-info">
        <el-icon><Select /></el-icon>
        <span>已选择 {{ selectedCourses.length }} 门课程</span>
      </div>
      <div class="batch-buttons">
        <el-button @click="batchMarkCompleted" type="success" :loading="isUpdating">
          <el-icon><Check /></el-icon>
          标记为已完成
        </el-button>
        <el-button @click="batchMarkIncomplete" type="warning" :loading="isUpdating">
          <el-icon><Close /></el-icon>
          标记为未完成
        </el-button>
        <el-button @click="selectedCourses = []" type="info">
          <el-icon><RefreshLeft /></el-icon>
          取消选择
        </el-button>
      </div>
    </div>

    <!-- 课程列表 -->
    <div class="courses-section">
      <el-card class="courses-card" shadow="never">
        <template #header>
          <div class="courses-header">
            <div class="header-left">
              <h3>
                <el-icon><Collection /></el-icon>
                课程列表
              </h3>
              <el-tag type="info" effect="light">
                共 {{ filteredCourses.length }} 门课程
              </el-tag>
            </div>
            <div class="header-actions">
              <el-tooltip content="全选/取消全选">
                <el-checkbox
                  v-model="selectAll"
                  :indeterminate="isIndeterminate"
                  @change="handleSelectAll"
                  size="large"
                >
                  全选
                </el-checkbox>
              </el-tooltip>
              <el-button 
                @click="refreshCourses" 
                :loading="isLoading"
                type="primary"
                circle
              >
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </div>
        </template>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>

        <!-- 空状态 -->
        <div v-else-if="filteredCourses.length === 0" class="empty-state">
          <div class="empty-icon">
            <el-icon size="64" color="#cbd5e0"><DocumentRemove /></el-icon>
          </div>
          <h3>没有找到课程</h3>
          <p v-if="hasActiveFilters">尝试调整筛选条件</p>
          <p v-else>系统中暂无课程数据</p>
          <el-button @click="resetFilters" type="primary" v-if="hasActiveFilters">
            清除筛选条件
          </el-button>
        </div>

        <!-- 课程表格 -->
        <div v-else class="courses-table-container">
          <el-table
            :data="paginatedCourses"
            style="width: 100%"
            class="courses-table"
            @selection-change="handleSelectionChange"
            stripe
            :cell-style="{ padding: '16px 0' }"
            :header-cell-style="{ 
              background: '#f8fafc', 
              color: '#374151',
              fontWeight: '600',
              padding: '16px 0'
            }"
          >
            <el-table-column type="selection" width="55" />
            
            <el-table-column prop="code" label="课程编号" width="120" sortable>
              <template #default="{ row }">
                <div class="course-code">{{ row.code }}</div>
              </template>
            </el-table-column>
            
            <el-table-column prop="name" label="课程名称" min-width="200" sortable>
              <template #default="{ row }">
                <div class="course-name">
                  <div class="name-text">{{ row.name }}</div>
                  <div class="course-meta">
                    <el-tag size="small" type="info" effect="light">
                      {{ row.credits }} 学分
                    </el-tag>
                    <span class="course-hours">{{ row.hours }}学时</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="module" label="模块" width="120" sortable>
              <template #default="{ row }">
                <el-tag 
                  :type="getModuleTagType(row.module)" 
                  effect="light"
                  class="module-tag"
                >
                  {{ row.module }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="teacher" label="任课教师" width="120">
              <template #default="{ row }">
                <div class="teacher-info">
                  <el-icon><UserFilled /></el-icon>
                  <span>{{ row.teacher || '未设置' }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="recommended_semester" label="建议学期" width="100" sortable>
              <template #default="{ row }">
                <el-tag 
                  size="small" 
                  :type="getSemesterTagType(row.recommended_semester)"
                  effect="light"
                >
                  第{{ row.recommended_semester }}学期
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column label="完成状态" width="120">
              <template #default="{ row }">
                <div class="status-switch">
                  <el-switch
                    v-model="row.isCompleted"
                    @change="toggleCourseStatus(row)"
                    :loading="row.isUpdating"
                    size="large"
                    active-text="已完成"
                    inactive-text="未完成"
                    :active-color="'#10b981'"
                    :inactive-color="'#6b7280'"
                  />
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="{ row }">
                <div class="course-actions">
                  <el-tooltip content="查看详情">
                    <el-button 
                      @click="viewCourseDetail(row)" 
                      type="primary" 
                      link
                      size="small"
                    >
                      <el-icon><View /></el-icon>
                    </el-button>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="filteredCourses.length"
              layout="total, sizes, prev, pager, next, jumper"
              background
              class="pagination"
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 课程详情弹窗 -->
    <el-dialog
      v-model="showDetailDialog"
      title="课程详情"
      width="600px"
      :before-close="handleCloseDetail"
      class="course-detail-dialog"
    >
      <div v-if="selectedCourse" class="course-detail">
        <div class="detail-header">
          <div class="course-title">
            <h2>{{ selectedCourse.name }}</h2>
            <el-tag 
              :type="selectedCourse.isCompleted ? 'success' : 'info'"
              size="large"
              effect="light"
            >
              {{ selectedCourse.isCompleted ? '已完成' : '未完成' }}
            </el-tag>
          </div>
          <div class="course-code-large">{{ selectedCourse.code }}</div>
        </div>
        
        <div class="detail-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="学分">
              <el-tag type="warning" effect="light">{{ selectedCourse.credits }} 学分</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="学时">
              {{ selectedCourse.hours }} 学时
            </el-descriptions-item>
            <el-descriptions-item label="课程模块">
              <el-tag :type="getModuleTagType(selectedCourse.module)" effect="light">
                {{ selectedCourse.module }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="建议学期">
              第{{ selectedCourse.recommended_semester }}学期
            </el-descriptions-item>
            <el-descriptions-item label="任课教师">
              {{ selectedCourse.teacher || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="课程性质">
              {{ selectedCourse.nature || '未知' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button 
            @click="toggleCourseStatusInDialog" 
            :type="selectedCourse?.isCompleted ? 'warning' : 'success'"
            :loading="selectedCourse?.isUpdating"
          >
            {{ selectedCourse?.isCompleted ? '标记为未完成' : '标记为已完成' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useProgressStore } from '../store/progress'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User, Filter, Search, RefreshLeft, Select, Check, Close, Collection,
  Refresh, DocumentRemove, UserFilled, View, 
  Trophy, TrendCharts, Warning
} from '@element-plus/icons-vue'

const progressStore = useProgressStore()

// 响应式数据
const searchKeyword = ref('')
const statusFilter = ref('')
const moduleFilter = ref('')
const selectedCourses = ref([])
const isUpdating = ref(false)
const isLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const selectAll = ref(false)
const showDetailDialog = ref(false)
const selectedCourse = ref(null)

// 模拟课程数据
const allCourses = ref([
  {
    id: 1, code: 'CS101', name: '计算机科学导论', credits: 3, hours: 48,
    module: '专业基础', teacher: '张教授', recommended_semester: 1,
    nature: '必修', isCompleted: true, isUpdating: false
  },
  {
    id: 2, code: 'MATH201', name: '高等数学A', credits: 5, hours: 80,
    module: '数学基础', teacher: '李教授', recommended_semester: 1,
    nature: '必修', isCompleted: false, isUpdating: false
  },
  {
    id: 3, code: 'CS102', name: '程序设计基础', credits: 4, hours: 64,
    module: '专业核心', teacher: '王教授', recommended_semester: 1,
    nature: '必修', isCompleted: true, isUpdating: false
  },
  {
    id: 4, code: 'CS201', name: '数据结构与算法', credits: 4, hours: 64,
    module: '专业核心', teacher: '赵教授', recommended_semester: 2,
    nature: '必修', isCompleted: false, isUpdating: false
  },
  {
    id: 5, code: 'CS301', name: '软件工程', credits: 3, hours: 48,
    module: '专业核心', teacher: '陈教授', recommended_semester: 3,
    nature: '必修', isCompleted: false, isUpdating: false
  }
])

// 计算属性
const filteredCourses = computed(() => {
  let courses = allCourses.value

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    courses = courses.filter(course =>
      course.name.toLowerCase().includes(keyword) ||
      course.code.toLowerCase().includes(keyword) ||
      (course.teacher && course.teacher.toLowerCase().includes(keyword))
    )
  }

  if (statusFilter.value) {
    courses = courses.filter(course => {
      if (statusFilter.value === 'completed') return course.isCompleted
      if (statusFilter.value === 'not_completed') return !course.isCompleted
      return true
    })
  }

  if (moduleFilter.value) {
    courses = courses.filter(course => course.module === moduleFilter.value)
  }

  return courses
})

const paginatedCourses = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredCourses.value.slice(start, end)
})

const uniqueModules = computed(() => {
  const modules = [...new Set(allCourses.value.map(course => course.module))]
  return modules.sort()
})

const hasActiveFilters = computed(() => {
  return searchKeyword.value || statusFilter.value || moduleFilter.value
})

const isIndeterminate = computed(() => {
  const selected = selectedCourses.value.length
  const total = paginatedCourses.value.length
  return selected > 0 && selected < total
})

const completedCount = computed(() => {
  return allCourses.value.filter(course => course.isCompleted).length
})

const completionRate = computed(() => {
  if (allCourses.value.length === 0) return 0
  return Math.round((completedCount.value / allCourses.value.length) * 100)
})

const quickStats = computed(() => [
  {
    key: 'total',
    label: '总课程数',
    value: allCourses.value.length,
    icon: Collection,
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    key: 'completed',
    label: '已完成',
    value: completedCount.value,
    icon: Trophy,
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
  },
  {
    key: 'rate',
    label: '完成率',
    value: `${completionRate.value}%`,
    icon: TrendCharts,
    gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
  },
  {
    key: 'pending',
    label: '待完成',
    value: allCourses.value.length - completedCount.value,
    icon: Warning,
    gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
  }
])

// 方法
const refreshCourses = async () => {
  isLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('课程数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败，请重试')
  } finally {
    isLoading.value = false
  }
}

const resetFilters = () => {
  searchKeyword.value = ''
  statusFilter.value = ''
  moduleFilter.value = ''
  currentPage.value = 1
}

const handleSelectionChange = (selection) => {
  selectedCourses.value = selection
}

const handleSelectAll = (checked) => {
  if (checked) {
    selectedCourses.value = [...paginatedCourses.value]
  } else {
    selectedCourses.value = []
  }
}

const toggleCourseStatus = async (course) => {
  course.isUpdating = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    course.isCompleted = !course.isCompleted
    ElMessage.success(`课程状态已更新为${course.isCompleted ? '已完成' : '未完成'}`)
  } catch (error) {
    ElMessage.error('更新失败，请重试')
    course.isCompleted = !course.isCompleted // 回滚
  } finally {
    course.isUpdating = false
  }
}

const batchMarkCompleted = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要将选中的 ${selectedCourses.value.length} 门课程标记为已完成吗？`,
      '批量操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    isUpdating.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    selectedCourses.value.forEach(course => {
      course.isCompleted = true
    })
    
    ElMessage.success(`已成功标记 ${selectedCourses.value.length} 门课程为已完成`)
    selectedCourses.value = []
  } catch {
    // 用户取消
  } finally {
    isUpdating.value = false
  }
}

const batchMarkIncomplete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要将选中的 ${selectedCourses.value.length} 门课程标记为未完成吗？`,
      '批量操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    isUpdating.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    selectedCourses.value.forEach(course => {
      course.isCompleted = false
    })
    
    ElMessage.success(`已成功标记 ${selectedCourses.value.length} 门课程为未完成`)
    selectedCourses.value = []
  } catch {
    // 用户取消
  } finally {
    isUpdating.value = false
  }
}

const viewCourseDetail = (course) => {
  selectedCourse.value = course
  showDetailDialog.value = true
}

const handleCloseDetail = () => {
  showDetailDialog.value = false
  selectedCourse.value = null
}

const toggleCourseStatusInDialog = async () => {
  if (selectedCourse.value) {
    await toggleCourseStatus(selectedCourse.value)
  }
}

const getModuleTagType = (module) => {
  const typeMap = {
    '专业核心': 'danger',
    '专业基础': 'warning',
    '数学基础': 'success',
    '通识教育': 'info',
    '实践教学': 'primary'
  }
  return typeMap[module] || 'info'
}

const getSemesterTagType = (semester) => {
  if (semester <= 2) return 'success'
  if (semester <= 4) return 'warning'
  if (semester <= 6) return 'danger'
  return 'info'
}

// 监听器
watch([searchKeyword, statusFilter, moduleFilter], () => {
  currentPage.value = 1
})

watch(selectedCourses, (newVal) => {
  const total = paginatedCourses.value.length
  selectAll.value = newVal.length === total && total > 0
})

onMounted(() => {
  refreshCourses()
})
</script>

<style scoped>
.my-courses-page {
  padding: 0;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  color: #667eea;
}

.page-description {
  margin: 0;
  color: #718096;
  font-size: 16px;
  line-height: 1.5;
}

.quick-stats {
  display: flex;
  gap: 16px;
}

.stat-card.mini {
  display: flex;
  align-items: center;
  gap: 12px;
  background: white;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  animation: slideInRight 0.6s ease-out both;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.stat-card.mini:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stat-card.mini .stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.mini .stat-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-card.mini .stat-number {
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
}

.stat-card.mini .stat-label {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
}

.filters-section {
  margin-bottom: 24px;
}

.filters-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.filters-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
}

.filters-content {
  /* 内容样式已在子元素中定义 */
}

.filter-item {
  margin-bottom: 16px;
}

.filter-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 24px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  animation: slideInDown 0.4s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.batch-buttons {
  display: flex;
  gap: 12px;
}

.courses-section {
  margin-bottom: 32px;
}

.courses-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.courses-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.loading-container {
  padding: 40px 0;
}

.empty-state {
  text-align: center;
  padding: 80px 40px;
  color: #718096;
}

.empty-icon {
  margin-bottom: 24px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.empty-state p {
  margin: 0 0 24px 0;
  font-size: 14px;
}

.courses-table-container {
  /* 表格容器样式由子元素定义 */
}

.courses-table {
  border-radius: 8px;
  overflow: hidden;
}

.course-code {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
  color: #667eea;
  background: #f8fafc;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.course-name {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.name-text {
  font-weight: 600;
  color: #1a202c;
  line-height: 1.4;
}

.course-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.course-hours {
  font-size: 12px;
  color: #718096;
  background: #f7fafc;
  padding: 2px 6px;
  border-radius: 4px;
}

.module-tag {
  font-weight: 500;
  border-radius: 6px;
}

.teacher-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #718096;
  font-size: 14px;
}

.status-switch {
  display: flex;
  justify-content: center;
}

.course-actions {
  display: flex;
  justify-content: center;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.pagination {
  /* Element Plus 组件样式 */
}

.course-detail-dialog {
  border-radius: 12px;
}

.course-detail {
  /* 详情样式 */
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.course-title {
  flex: 1;
}

.course-title h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.3;
}

.course-code-large {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
  color: #667eea;
  background: #f8fafc;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  border: 1px solid #e2e8f0;
}

.detail-content {
  /* 描述列表样式由 Element Plus 提供 */
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .header-content h1 {
    font-size: 28px;
  }
  
  .quick-stats {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 8px;
  }
  
  .stat-card.mini {
    flex-shrink: 0;
    min-width: 140px;
  }
  
  .filters-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .batch-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .batch-buttons {
    justify-content: center;
  }
  
  .courses-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .detail-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .course-code-large {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .header-content h1 {
    font-size: 24px;
  }
  
  .quick-stats {
    gap: 12px;
  }
  
  .stat-card.mini {
    min-width: 120px;
    padding: 12px 16px;
  }
  
  .course-detail-dialog {
    margin: 20px;
    width: calc(100vw - 40px);
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .my-courses-page {
    color: #f7fafc;
  }
  
  .stat-card.mini,
  .filters-card,
  .courses-card {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .course-code,
  .course-code-large {
    background: #374151;
    color: #93c5fd;
  }
  
  .course-hours {
    background: #374151;
    color: #d1d5db;
  }
}

/* 表格样式增强 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__row:hover > td) {
  background-color: #f8fafc !important;
}

:deep(.el-table th.el-table__cell) {
  background: #f8fafc !important;
  border-bottom: 2px solid #e5e7eb !important;
}

:deep(.el-table .el-table__row) {
  transition: background-color 0.3s ease;
}

:deep(.el-table .el-table__row.current-row > td) {
  background-color: #eff6ff !important;
}

/* 开关样式增强 */
:deep(.el-switch.is-checked .el-switch__core) {
  background-color: #10b981 !important;
  border-color: #10b981 !important;
}

:deep(.el-switch__core) {
  transition: all 0.3s ease;
}

/* 对话框样式增强 */
:deep(.course-detail-dialog .el-dialog) {
  border-radius: 12px;
}

:deep(.course-detail-dialog .el-dialog__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px 12px 0 0;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.course-detail-dialog .el-dialog__title) {
  font-weight: 600;
  color: #374151;
}
</style> 