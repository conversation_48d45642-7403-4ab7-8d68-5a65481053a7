from django.urls import path
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from .views import RegisterView, CourseListView, MyRecordsView, AuditView

urlpatterns = [
    # 认证/授权
    path('auth/register/', RegisterView.as_view(), name='auth_register'),
    path('auth/login/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # 核心 API
    path('courses/', CourseListView.as_view(), name='course_list'),
    path('me/records/', MyRecordsView.as_view(), name='my_records'),
    path('me/audit/', AuditView.as_view(), name='audit'),
]
