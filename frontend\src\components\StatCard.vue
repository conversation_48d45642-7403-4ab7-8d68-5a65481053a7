<template>
  <GlassCard 
    :variant="variant" 
    hoverable 
    class="stat-card"
    :class="{ 'is-featured': featured }"
  >
    <div class="stat-content">
      <div class="stat-header">
        <div class="stat-icon-wrapper" :class="variant">
          <el-icon :size="iconSize">
            <component :is="icon" />
          </el-icon>
        </div>
        
        <div v-if="trend" class="stat-trend" :class="trend.type">
          <el-icon class="trend-icon">
            <ArrowUp v-if="trend.type === 'up'" />
            <ArrowDown v-if="trend.type === 'down'" />
            <Minus v-if="trend.type === 'neutral'" />
          </el-icon>
          <span class="trend-value">{{ trend.value }}%</span>
        </div>
      </div>
      
      <div class="stat-body">
        <div class="stat-number">
          <AnimatedNumber 
            :value="value" 
            :duration="1200"
            :prefix="prefix"
            :suffix="suffix"
            :decimals="decimals"
          />
        </div>
        
        <div class="stat-label">{{ label }}</div>
        
        <div v-if="description" class="stat-description">
          {{ description }}
        </div>
      </div>
      
      <div v-if="progress !== undefined" class="stat-footer">
        <div class="progress-container">
          <el-progress 
            :percentage="progress"
            :stroke-width="6"
            :show-text="false"
            :color="getProgressColor(progress)"
            class="progress-bar"
          />
          <div class="progress-text">{{ progress }}%</div>
        </div>
      </div>
      
      <div v-if="showRing && progress !== undefined" class="stat-ring">
        <ProgressRing 
          :percentage="progress" 
          :size="80" 
          :color="getProgressColor(progress)"
        />
      </div>
    </div>
    
    <!-- 装饰性效果 -->
    <div class="stat-decoration">
      <div class="decoration-dot dot-1"></div>
      <div class="decoration-dot dot-2"></div>
      <div class="decoration-dot dot-3"></div>
    </div>
  </GlassCard>
</template>

<script setup lang="ts">
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'
import GlassCard from './GlassCard.vue'
import AnimatedNumber from './AnimatedNumber.vue'
import ProgressRing from './ProgressRing.vue'

interface TrendData {
  type: 'up' | 'down' | 'neutral'
  value: number
}

interface Props {
  label: string
  value: number
  icon: string
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'error'
  prefix?: string
  suffix?: string
  decimals?: number
  description?: string
  progress?: number
  trend?: TrendData
  featured?: boolean
  showRing?: boolean
  iconSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  decimals: 0,
  featured: false,
  showRing: false,
  iconSize: 24
})

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#10b981'
  if (percentage >= 60) return '#3b82f6'
  if (percentage >= 40) return '#f59e0b'
  return '#ef4444'
}
</script>

<style scoped>
.stat-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card.is-featured {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
  border-color: rgba(59, 130, 246, 0.3);
}

.stat-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 40px rgba(31, 38, 135, 0.3);
}

.stat-content {
  padding: 1.5rem;
  position: relative;
  z-index: 2;
}

.stat-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.stat-icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.stat-icon-wrapper.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.25);
}

.stat-icon-wrapper.success {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.25);
}

.stat-icon-wrapper.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 8px 24px rgba(245, 158, 11, 0.25);
}

.stat-icon-wrapper.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 8px 24px rgba(239, 68, 68, 0.25);
}

.stat-icon-wrapper.default {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  box-shadow: 0 8px 24px rgba(107, 114, 128, 0.25);
}

.stat-icon-wrapper .el-icon {
  color: white;
  z-index: 1;
}

.stat-icon-wrapper::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.stat-trend.up {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.stat-trend.down {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.stat-trend.neutral {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.trend-icon {
  font-size: 0.75rem;
}

.stat-body {
  margin-bottom: 1rem;
}

.stat-number {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.stat-description {
  font-size: 0.875rem;
  color: var(--text-tertiary);
  line-height: 1.4;
}

.stat-footer {
  margin-top: 1rem;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.progress-bar {
  flex: 1;
}

.progress-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  min-width: 40px;
  text-align: right;
}

.stat-ring {
  position: absolute;
  top: 1rem;
  right: 1rem;
  opacity: 0.8;
}

.stat-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-dot {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  animation: float 4s ease-in-out infinite;
}

.dot-1 {
  top: 20%;
  right: 20%;
  animation-delay: 0s;
}

.dot-2 {
  top: 60%;
  right: 30%;
  animation-delay: -1s;
}

.dot-3 {
  top: 80%;
  right: 10%;
  animation-delay: -2s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); opacity: 0.3; }
  50% { transform: translateY(-10px); opacity: 0.6; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-content {
    padding: 1.25rem;
  }
  
  .stat-number {
    font-size: 1.875rem;
  }
  
  .stat-icon-wrapper {
    width: 48px;
    height: 48px;
  }
  
  .stat-ring {
    display: none;
  }
}
</style>
