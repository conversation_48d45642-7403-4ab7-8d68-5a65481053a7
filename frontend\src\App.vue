<script setup lang="ts">
import { onMounted } from 'vue'
import { useUserStore } from './store/user'
import { useTheme } from './composables/useTheme'

const userStore = useUserStore()
const { theme } = useTheme()

onMounted(() => {
  userStore.init()
})
</script>

<template>
  <div id="app" :class="theme">
    <router-view />
  </div>
</template>

<style>
#app {
  min-height: 100vh;
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: all 0.3s ease;
}

/* 全局过渡动画 */
.v-enter-active,
.v-leave-active {
  transition: all 0.3s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style> 