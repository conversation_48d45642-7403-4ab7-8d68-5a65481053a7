{"name": "courseflow-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.5.0", "animate.css": "^4.1.1", "axios": "^1.6.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "^2.4.0", "gsap": "^3.12.2", "lucide-vue-next": "^0.294.0", "nprogress": "^0.2.0", "particles.js": "^2.0.0", "perfect-scrollbar": "^1.5.5", "pinia": "^2.1.7", "sortablejs": "^1.15.1", "vue": "^3.4.0", "vue-echarts": "^6.6.0", "vue-particles": "^1.0.9", "vue-router": "^4.3.0", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-lottie": "^3.3.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "@vue/tsconfig": "^0.5.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "sass": "^1.89.2", "sass-embedded": "^1.89.2", "tailwindcss": "^3.3.6", "typescript": "^5.3.0", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.0", "vue-tsc": "^1.8.25"}}