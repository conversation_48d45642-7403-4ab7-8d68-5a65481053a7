<template>
  <div class="glass-card" :class="[variant, { 'is-hoverable': hoverable }]">
    <div v-if="$slots.header" class="glass-card-header">
      <slot name="header" />
    </div>
    
    <div class="glass-card-body">
      <slot />
    </div>
    
    <div v-if="$slots.footer" class="glass-card-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'error'
  hoverable?: boolean
}

withDefaults(defineProps<Props>(), {
  variant: 'default',
  hoverable: false,
})
</script>

<style scoped>
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
}

.glass-card.is-hoverable {
  cursor: pointer;
}

.glass-card.is-hoverable:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
  border-color: rgba(255, 255, 255, 0.3);
}

.glass-card.primary {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.glass-card.success {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

.glass-card.warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

.glass-card.error {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.glass-card-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-card-body {
  padding: 24px;
}

.glass-card-footer {
  padding: 16px 24px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

/* 暗黑主题适配 */
[data-theme="dark"] .glass-card {
  background: rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .glass-card.primary {
  background: rgba(59, 130, 246, 0.15);
}

[data-theme="dark"] .glass-card.success {
  background: rgba(16, 185, 129, 0.15);
}

[data-theme="dark"] .glass-card.warning {
  background: rgba(245, 158, 11, 0.15);
}

[data-theme="dark"] .glass-card.error {
  background: rgba(239, 68, 68, 0.15);
}
</style>
