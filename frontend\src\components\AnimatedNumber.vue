<template>
  <div 
    class="animated-number"
    :class="{ 'is-animating': isAnimating }"
  >
    {{ displayValue }}
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

interface Props {
  value: number
  duration?: number
  decimals?: number
  prefix?: string
  suffix?: string
  formatFn?: (value: number) => string
}

const props = withDefaults(defineProps<Props>(), {
  duration: 1000,
  decimals: 0,
  prefix: '',
  suffix: '',
})

const displayValue = ref('0')
const isAnimating = ref(false)

const formatNumber = (value: number): string => {
  if (props.formatFn) {
    return props.formatFn(value)
  }
  
  const formatted = value.toFixed(props.decimals)
  return `${props.prefix}${formatted}${props.suffix}`
}

const animateToValue = (targetValue: number) => {
  const startValue = parseFloat(displayValue.value.replace(/[^\d.-]/g, '')) || 0
  const startTime = performance.now()
  
  isAnimating.value = true
  
  const animate = (currentTime: number) => {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / props.duration, 1)
    
    // 缓动函数
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    const currentValue = startValue + (targetValue - startValue) * easeOutQuart
    
    displayValue.value = formatNumber(currentValue)
    
    if (progress < 1) {
      requestAnimationFrame(animate)
    } else {
      isAnimating.value = false
    }
  }
  
  requestAnimationFrame(animate)
}

watch(() => props.value, (newValue) => {
  animateToValue(newValue)
})

onMounted(() => {
  displayValue.value = formatNumber(0)
  if (props.value !== 0) {
    setTimeout(() => animateToValue(props.value), 300)
  }
})
</script>

<style scoped>
.animated-number {
  font-variant-numeric: tabular-nums;
  transition: color 0.3s ease;
}

.animated-number.is-animating {
  color: var(--primary-color);
}
</style>
