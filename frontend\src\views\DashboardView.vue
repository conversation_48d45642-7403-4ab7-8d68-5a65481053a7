<template>
  <div class="dashboard">
    <!-- 背景装饰 -->
    <div class="dashboard-bg">
      <div class="bg-gradient"></div>
      <div class="bg-pattern"></div>
    </div>

    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="welcome-section">
          <h1 class="welcome-title">
            <span class="gradient-text">欢迎回来</span>
            <el-icon class="wave-icon"><WindPower /></el-icon>
          </h1>
          <p class="welcome-subtitle">{{ getWelcomeMessage() }}</p>
        </div>
        
        <div class="header-actions">
          <el-button 
            @click="refreshData" 
            :loading="progressStore.isLoading" 
            type="primary"
            size="large"
            class="refresh-btn"
          >
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <LoadingOverlay 
      :visible="progressStore.isLoading && !hasInitialData" 
      text="正在加载学分数据..."
    />

    <!-- 主要内容 -->
    <div v-if="progressStore.auditReport" class="dashboard-content">
      <!-- 统计卡片 -->
      <div class="stats-section">
        <div class="section-header">
          <h2 class="section-title">学分统计概览</h2>
          <p class="section-subtitle">实时追踪您的学习进度</p>
        </div>
        
        <div class="stats-grid">
          <GlassCard 
            v-for="(stat, index) in statsData" 
            :key="stat.key"
            :variant="stat.variant"
            hoverable
            class="stat-card animate__animated animate__fadeInUp"
            :style="{ animationDelay: `${index * 0.1}s` }"
          >
            <div class="stat-content">
              <div class="stat-icon-wrapper" :class="stat.variant">
                <el-icon :size="32">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              
              <div class="stat-info">
                <div class="stat-number">
                  <AnimatedNumber 
                    :value="stat.value" 
                    :duration="1200"
                    :suffix="stat.suffix"
                  />
                </div>
                <div class="stat-label">{{ stat.label }}</div>
                <div v-if="stat.description" class="stat-description">
                  {{ stat.description }}
                </div>
              </div>
              
              <div v-if="stat.progress !== undefined" class="stat-progress">
                <el-progress 
                  :percentage="stat.progress"
                  :stroke-width="8"
                  :show-text="false"
                  :color="getProgressColor(stat.progress)"
                  class="progress-bar"
                />
                <div class="progress-text">{{ stat.progress }}%</div>
              </div>
            </div>
          </GlassCard>
        </div>
      </div>

      <!-- 详细信息和图表 -->
      <div class="details-section">
        <div class="details-grid">
          <!-- 学分分布图表 -->
          <GlassCard class="chart-card">
            <template #header>
              <div class="card-header">
                <div class="header-info">
                  <el-icon class="header-icon"><PieChart /></el-icon>
                  <div>
                    <h3>学分分布</h3>
                    <p>各模块完成情况详细分析</p>
                  </div>
                </div>
                <el-button type="primary" link @click="$router.push('/audit')">
                  查看详情 <el-icon><ArrowRight /></el-icon>
                </el-button>
              </div>
            </template>
            
            <div class="chart-container">
              <div id="creditChart" class="chart"></div>
            </div>
          </GlassCard>

          <!-- 最近动态 -->
          <GlassCard class="activity-card">
            <template #header>
              <div class="card-header">
                <div class="header-info">
                  <el-icon class="header-icon"><Clock /></el-icon>
                  <div>
                    <h3>最近动态</h3>
                    <p>您的学习轨迹</p>
                  </div>
                </div>
              </div>
            </template>
            
            <div class="activity-list">
              <div 
                v-for="(activity, index) in recentActivities" 
                :key="index"
                class="activity-item"
              >
                <div class="activity-icon" :class="activity.type">
                  <el-icon>
                    <component :is="activity.icon" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-description">{{ activity.description }}</div>
                  <div class="activity-time">{{ activity.time }}</div>
                </div>
              </div>
            </div>
          </GlassCard>
        </div>

        <!-- 课程进度详情 -->
        <GlassCard class="progress-card">
          <template #header>
            <div class="card-header">
              <div class="header-info">
                <el-icon class="header-icon"><Grid /></el-icon>
                <div>
                  <h3>各模块学分详情</h3>
                  <p>详细的学分分类完成情况</p>
                </div>
              </div>
            </div>
          </template>
          
          <div class="progress-list">
            <div
              v-for="(category, index) in progressStore.auditReport.category_details"
              :key="category.name"
              class="progress-item"
              :style="{ animationDelay: `${index * 0.1}s` }"
            >
              <div class="progress-header">
                <div class="category-info">
                  <span class="category-name">{{ category.name }}</span>
                  <span class="category-status" :class="getCategoryStatus(category)">
                    {{ getCategoryStatusText(category) }}
                  </span>
                </div>
                <div class="category-credits">
                  <AnimatedNumber :value="category.completed" />
                  <span class="credits-separator">/</span>
                  <span class="credits-total">{{ category.required }}</span>
                  <span class="credits-unit">学分</span>
                </div>
              </div>
              
              <div class="progress-bar-container">
                <el-progress 
                  :percentage="getProgressPercentage(category.completed, category.required)"
                  :stroke-width="8"
                  :show-text="false"
                  :color="getProgressColor(getProgressPercentage(category.completed, category.required))"
                />
              </div>
              
              <div v-if="category.courses && category.courses.length > 0" class="course-list">
                <div class="course-header">
                  <span>已完成课程</span>
                  <el-tag size="small" type="info">{{ category.courses.length }} 门</el-tag>
                </div>
                <div class="course-tags">
                  <el-tag 
                    v-for="course in category.courses.slice(0, 5)" 
                    :key="course.id"
                    size="small"
                    class="course-tag"
                  >
                    {{ course.name }}
                  </el-tag>
                  <el-tag 
                    v-if="category.courses.length > 5"
                    size="small"
                    type="info"
                  >
                    +{{ category.courses.length - 5 }} 门
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </GlassCard>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!progressStore.isLoading" class="empty-state">
      <div class="empty-content">
        <el-icon class="empty-icon" size="80"><DocumentRemove /></el-icon>
        <h3>暂无数据</h3>
        <p>请先刷新数据或联系管理员</p>
        <el-button type="primary" @click="refreshData">
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useProgressStore } from '../store/progress'
import { useUserStore } from '../store/user'
import * as echarts from 'echarts'
import GlassCard from '../components/GlassCard.vue'
import AnimatedNumber from '../components/AnimatedNumber.vue'
import LoadingOverlay from '../components/LoadingOverlay.vue'

const progressStore = useProgressStore()
const userStore = useUserStore()

const hasInitialData = ref(false)

// 统计数据
const statsData = computed(() => {
  const report = progressStore.auditReport
  if (!report) return []

  const totalCompleted = report.category_details.reduce((sum, cat) => sum + cat.completed, 0)
  const totalRequired = report.category_details.reduce((sum, cat) => sum + cat.required, 0)
  const completionRate = Math.round((totalCompleted / totalRequired) * 100) || 0
  
  return [
    {
      key: 'total',
      label: '总学分',
      value: totalCompleted,
      suffix: ` / ${totalRequired}`,
      description: '已完成学分',
      progress: completionRate,
      icon: 'Trophy',
      variant: 'primary'
    },
    {
      key: 'completed',
      label: '已完成',
      value: totalCompleted,
      suffix: ' 学分',
      description: '累计获得',
      icon: 'CircleCheckFilled',
      variant: 'success'
    },
    {
      key: 'remaining',
      label: '剩余学分',
      value: Math.max(0, totalRequired - totalCompleted),
      suffix: ' 学分',
      description: '还需完成',
      icon: 'Clock',
      variant: 'warning'
    },
    {
      key: 'progress',
      label: '完成度',
      value: completionRate,
      suffix: '%',
      description: '整体进度',
      icon: 'TrendCharts',
      variant: 'default'
    }
  ]
})

// 最近动态数据
const recentActivities = computed(() => [
  {
    type: 'success',
    icon: 'CircleCheckFilled',
    title: '课程完成',
    description: '《软件工程基础》课程已完成',
    time: '2 小时前'
  },
  {
    type: 'primary',
    icon: 'DocumentAdd',
    title: '数据更新',
    description: '学分数据已同步更新',
    time: '1 天前'
  },
  {
    type: 'warning',
    icon: 'WarningFilled',
    title: '注意提醒',
    description: '部分课程学分待确认',
    time: '3 天前'
  }
])

// 工具函数
const getWelcomeMessage = () => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好！开始新的一天学习吧 🌅'
  if (hour < 18) return '下午好！继续努力完成学业目标 ☀️'
  return '晚上好！回顾今天的学习成果 🌙'
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#10b981'
  if (percentage >= 60) return '#3b82f6'
  if (percentage >= 40) return '#f59e0b'
  return '#ef4444'
}

const getProgressPercentage = (completed: number, required: number) => {
  return Math.round((completed / required) * 100) || 0
}

const getCategoryStatus = (category: any) => {
  const percentage = getProgressPercentage(category.completed, category.required)
  if (percentage >= 100) return 'completed'
  if (percentage >= 80) return 'almost'
  if (percentage >= 50) return 'progress'
  return 'start'
}

const getCategoryStatusText = (category: any) => {
  const percentage = getProgressPercentage(category.completed, category.required)
  if (percentage >= 100) return '已完成'
  if (percentage >= 80) return '即将完成'
  if (percentage >= 50) return '进行中'
  return '刚开始'
}

const refreshData = async () => {
  await progressStore.fetchAuditReport()
  hasInitialData.value = true
  await nextTick()
  initChart()
}

// 初始化图表
const initChart = () => {
  const chartDom = document.getElementById('creditChart')
  if (!chartDom || !progressStore.auditReport) return

  const myChart = echarts.init(chartDom)
  const data = progressStore.auditReport.category_details.map(cat => ({
    name: cat.name,
    value: cat.completed,
    required: cat.required
  }))

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} 学分 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        color: 'var(--text-primary)'
      }
    },
    series: [
      {
        name: '学分分布',
        type: 'pie',
        radius: '50%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        }
      }
    ]
  }

  myChart.setOption(option)
  
  // 响应式调整
  window.addEventListener('resize', () => myChart.resize())
}

onMounted(async () => {
  if (progressStore.auditReport) {
    hasInitialData.value = true
    await nextTick()
    initChart()
  } else {
    await refreshData()
  }
})
</script>

<style scoped>
.dashboard {
  min-height: calc(100vh - 144px);
  position: relative;
  overflow: hidden;
}

.dashboard-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1) 0%, 
    rgba(16, 185, 129, 0.05) 50%, 
    transparent 100%);
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  background-size: 400px 400px;
  animation: float 20s ease-in-out infinite;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.welcome-section {
  flex: 1;
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-color), var(--success-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.wave-icon {
  color: var(--warning-color);
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.welcome-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.refresh-btn {
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.stats-section {
  margin-bottom: 1rem;
}

.section-header {
  text-align: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.section-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  padding: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-content {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stat-icon-wrapper {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
}

.stat-icon-wrapper.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.stat-icon-wrapper.success {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
}

.stat-icon-wrapper.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 8px 24px rgba(245, 158, 11, 0.3);
}

.stat-icon-wrapper.default {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  box-shadow: 0 8px 24px rgba(107, 114, 128, 0.3);
}

.stat-icon-wrapper .el-icon {
  color: white;
  z-index: 1;
}

.stat-icon-wrapper::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.stat-description {
  font-size: 0.875rem;
  color: var(--text-tertiary);
}

.stat-progress {
  margin-top: 1rem;
}

.progress-bar {
  margin-bottom: 0.5rem;
}

.progress-text {
  text-align: right;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.details-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.chart-card,
.activity-card,
.progress-card {
  min-height: 400px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.header-info h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.header-info p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.chart-container {
  height: 300px;
  width: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.7);
  transform: translateX(4px);
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

.activity-icon.success {
  background: var(--success-color);
}

.activity-icon.primary {
  background: var(--primary-color);
}

.activity-icon.warning {
  background: var(--warning-color);
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.activity-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.progress-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.progress-item {
  padding: 1.5rem;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.progress-item:hover {
  background: rgba(255, 255, 255, 0.7);
  transform: translateY(-2px);
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.category-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.category-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-status.completed {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success-color);
}

.category-status.almost {
  background: rgba(59, 130, 246, 0.2);
  color: var(--primary-color);
}

.category-status.progress {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning-color);
}

.category-status.start {
  background: rgba(107, 114, 128, 0.2);
  color: var(--text-tertiary);
}

.category-credits {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.credits-separator {
  color: var(--text-tertiary);
  margin: 0 0.25rem;
}

.credits-total {
  color: var(--text-secondary);
}

.credits-unit {
  font-size: 0.875rem;
  color: var(--text-tertiary);
  margin-left: 0.25rem;
}

.progress-bar-container {
  margin-bottom: 1rem;
}

.course-list {
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.course-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.course-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.course-tag {
  font-size: 0.75rem;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
  padding: 2rem;
}

.empty-icon {
  color: var(--text-tertiary);
  margin-bottom: 1rem;
}

.empty-content h3 {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.empty-content p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .welcome-title {
    font-size: 2rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .course-header {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
