import { createRouter, createWebHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import LayoutView from '../views/LayoutView.vue'
import DashboardView from '../views/DashboardView.vue'
import MyCoursesView from '../views/MyCoursesView.vue'
import CourseLibraryView from '../views/CourseLibraryView.vue'
import AuditView from '../views/AuditView.vue'
import { useUserStore } from '../store/user'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginView
    },
    {
      path: '/',
      component: LayoutView,
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          redirect: '/dashboard'
        },
        {
          path: 'dashboard',
          name: 'dashboard',
          component: DashboardView,
          meta: { 
            requiresAuth: true,
            title: '仪表盘',
            icon: 'Odometer'
          }
        },
        {
          path: 'my-courses',
          name: 'my-courses',
          component: MyCoursesView,
          meta: { 
            requiresAuth: true,
            title: '我的课程',
            icon: 'User'
          }
        },
        {
          path: 'course-library',
          name: 'course-library',
          component: CourseLibraryView,
          meta: { 
            requiresAuth: true,
            title: '课程库',
            icon: 'Collection'
          }
        },
        {
          path: 'audit',
          name: 'audit',
          component: AuditView,
          meta: { 
            requiresAuth: true,
            title: '毕业审核',
            icon: 'Document'
          }
        }
      ]
    }
  ]
})

router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  if (to.meta.requiresAuth && !userStore.isAuthenticated) {
    next({ name: 'login' })
  } else {
    next()
  }
})

export default router 