import json
import yaml
from django.core.management.base import BaseCommand, CommandError
from academic.models import Course

class Command(BaseCommand):
    help = 'Imports courses from a specified JSON or YAML file.'

    def add_arguments(self, parser):
        parser.add_argument('file_path', type=str, help='The path to the course data file (JSON or YAML).')

    def handle(self, *args, **options):
        file_path = options['file_path']
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.endswith('.json'):
                    data = json.load(f)
                elif file_path.endswith('.yaml') or file_path.endswith('.yml'):
                    data = yaml.safe_load(f)
                else:
                    raise CommandError("Unsupported file format. Please use .json, .yaml, or .yml")
        except FileNotFoundError:
            raise CommandError(f'File not found at: {file_path}')
        except Exception as e:
            raise CommandError(f"Error parsing file: {e}")

        if not isinstance(data, list):
            raise CommandError("The data file should contain a list of course objects.")

        created_count = 0
        updated_count = 0

        for course_data in data:
            course_code = course_data.get('course_code')
            if not course_code:
                self.stdout.write(self.style.WARNING(f"Skipping a record due to missing 'course_code': {course_data}"))
                continue
            
            # 使用 update_or_create 避免重复创建
            obj, created = Course.objects.update_or_create(
                course_code=course_code,
                defaults=course_data
            )

            if created:
                created_count += 1
                self.stdout.write(self.style.SUCCESS(f"Successfully created course: {obj.name}"))
            else:
                updated_count += 1
                self.stdout.write(self.style.NOTICE(f"Successfully updated course: {obj.name}"))

        self.stdout.write(self.style.SUCCESS(f"Import finished. Total created: {created_count}. Total updated: {updated_count}.")) 