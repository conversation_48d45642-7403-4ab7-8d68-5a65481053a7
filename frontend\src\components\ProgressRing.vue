<template>
  <div class="progress-ring-container">
    <svg class="progress-ring" :width="size" :height="size">
      <circle
        class="progress-ring-background"
        :stroke-width="strokeWidth"
        :r="radius"
        :cx="size / 2"
        :cy="size / 2"
      />
      <circle
        class="progress-ring-progress"
        :stroke-width="strokeWidth"
        :stroke-dasharray="circumference + ' ' + circumference"
        :stroke-dashoffset="strokeDashoffset"
        :r="radius"
        :cx="size / 2"
        :cy="size / 2"
        :style="{ stroke: color }"
      />
    </svg>
    
    <div class="progress-ring-content">
      <div class="progress-value">
        <AnimatedNumber :value="percentage" suffix="%" />
      </div>
      <div v-if="label" class="progress-label">{{ label }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue'
import AnimatedNumber from './AnimatedNumber.vue'

interface Props {
  percentage: number
  size?: number
  strokeWidth?: number
  color?: string
  label?: string
  animated?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 120,
  strokeWidth: 8,
  color: '#3b82f6',
  animated: true
})

const radius = computed(() => (props.size - props.strokeWidth) / 2)
const circumference = computed(() => radius.value * 2 * Math.PI)
const strokeDashoffset = ref(circumference.value)

const animateProgress = () => {
  const targetOffset = circumference.value - (props.percentage / 100) * circumference.value
  
  if (props.animated) {
    const startTime = performance.now()
    const duration = 1500
    const startOffset = strokeDashoffset.value
    
    const animate = (currentTime: number) => {
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      // 缓动函数
      const easeOutCubic = 1 - Math.pow(1 - progress, 3)
      strokeDashoffset.value = startOffset + (targetOffset - startOffset) * easeOutCubic
      
      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    
    requestAnimationFrame(animate)
  } else {
    strokeDashoffset.value = targetOffset
  }
}

watch(() => props.percentage, () => {
  animateProgress()
})

onMounted(() => {
  setTimeout(() => animateProgress(), 300)
})
</script>

<style scoped>
.progress-ring-container {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  transform: rotate(-90deg);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.progress-ring-background {
  fill: transparent;
  stroke: rgba(0, 0, 0, 0.1);
}

.progress-ring-progress {
  fill: transparent;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.1s ease;
}

.progress-ring-content {
  position: absolute;
  text-align: center;
  pointer-events: none;
}

.progress-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.progress-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

/* 暗黑主题适配 */
[data-theme="dark"] .progress-ring-background {
  stroke: rgba(255, 255, 255, 0.2);
}
</style>
