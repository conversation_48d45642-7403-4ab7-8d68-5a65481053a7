import { defineStore } from 'pinia'
import axios from 'axios'

export const useProgressStore = defineStore('progress', {
  state: () => ({
    auditReport: null as any,
    isLoading: false,
  }),
  actions: {
    async fetchAuditReport() {
      this.isLoading = true
      try {
        const response = await axios.get('/api/me/audit/')
        this.auditReport = response.data
      } catch (error) {
        console.error('Failed to fetch audit report:', error)
        // 可以在此处理错误，例如用户 token 失效时，调用 userStore.logout()
      } finally {
        this.isLoading = false
      }
    },
  },
}) 