<template>
  <el-container class="layout-container">
    <!-- 侧边栏遮罩层 (移动端) -->
    <div 
      v-if="isMobile && !isCollapsed" 
      class="sidebar-overlay"
      @click="isCollapsed = true"
    ></div>

    <!-- 侧边栏 -->
    <el-aside class="sidebar" :class="{ collapsed: isCollapsed, mobile: isMobile }">
      <div class="sidebar-header">
        <div class="logo" @click="toggleSidebar">
          <div class="logo-icon">
            <el-icon size="28" color="#409EFF">
              <School />
            </el-icon>
          </div>
          <transition name="fade-slide">
            <h2 v-show="!isCollapsed" class="logo-text">学程助手</h2>
          </transition>
        </div>
      </div>
      
      <div class="menu-container">
        <el-menu
          :default-active="currentRoute"
          class="sidebar-menu"
          :collapse="isCollapsed"
          router
        >
          <el-menu-item index="/dashboard" class="menu-item">
            <el-icon class="menu-icon"><Odometer /></el-icon>
            <template #title>
              <span class="menu-title">仪表盘</span>
            </template>
          </el-menu-item>
          
          <el-menu-item index="/my-courses" class="menu-item">
            <el-icon class="menu-icon"><User /></el-icon>
            <template #title>
              <span class="menu-title">我的课程</span>
            </template>
          </el-menu-item>
          
          <el-menu-item index="/course-library" class="menu-item">
            <el-icon class="menu-icon"><Collection /></el-icon>
            <template #title>
              <span class="menu-title">课程库</span>
            </template>
          </el-menu-item>
          
          <el-menu-item index="/audit" class="menu-item">
            <el-icon class="menu-icon"><Document /></el-icon>
            <template #title>
              <span class="menu-title">毕业审核</span>
            </template>
          </el-menu-item>
        </el-menu>
      </div>
      
      <!-- 侧边栏底部 -->
      <div class="sidebar-footer">
        <el-dropdown 
          trigger="click" 
          placement="top-start"
          popper-class="user-dropdown"
        >
          <div class="user-profile" :class="{ collapsed: isCollapsed }">
            <el-avatar 
              :size="isCollapsed ? 32 : 40" 
              :icon="UserFilled"
              class="user-avatar"
            />
            <transition name="fade-slide">
              <div v-show="!isCollapsed" class="user-info">
                <div class="username">{{ userStore.userInfo?.username || '学生' }}</div>
                <div class="user-role">2024级软件工程</div>
              </div>
            </transition>
            <transition name="fade">
              <el-icon v-show="!isCollapsed" class="dropdown-icon">
                <ArrowUp />
              </el-icon>
            </transition>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="refreshData" class="dropdown-item">
                <el-icon><Refresh /></el-icon>
                <span>刷新数据</span>
              </el-dropdown-item>
              <el-dropdown-item divided @click="userStore.logout()" class="dropdown-item logout">
                <el-icon><SwitchButton /></el-icon>
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-aside>

    <!-- 主内容区 -->
    <el-container class="main-container">
      <!-- 顶部栏 -->
      <el-header class="main-header">
        <div class="header-left">
          <el-button 
            @click="toggleSidebar" 
            :icon="isCollapsed ? Expand : Fold" 
            circle
            size="default"
            class="collapse-btn"
          />
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item>
              <el-icon><School /></el-icon>
              学程助手
            </el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <el-tooltip content="切换主题" placement="bottom">
            <el-button 
              @click="toggleTheme" 
              circle
              class="header-btn theme-btn"
            >
              <el-icon>
                <Sunny v-if="getCurrentTheme() === 'light'" />
                <Moon v-else />
              </el-icon>
            </el-button>
          </el-tooltip>
          
          <el-tooltip content="预警提醒" placement="bottom">
            <el-badge 
              :value="warningCount > 0 ? warningCount : null" 
              class="warning-badge" 
              type="warning"
              :max="99"
            >
              <el-button 
                @click="$router.push('/audit')" 
                circle
                :type="warningCount > 0 ? 'warning' : 'default'"
                class="header-btn"
              >
                <el-icon>
                  <WarningFilled v-if="warningCount > 0" />
                  <CircleCheckFilled v-else />
                </el-icon>
              </el-button>
            </el-badge>
          </el-tooltip>
          
          <el-tooltip content="刷新数据" placement="bottom">
            <el-button 
              @click="refreshData" 
              circle
              :loading="progressStore.isLoading"
              class="header-btn"
            >
              <el-icon><Refresh /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </el-header>

      <!-- 主内容 -->
      <el-main class="main-content">
        <div class="page-content">
          <transition name="page-fade" mode="out-in">
            <router-view />
          </transition>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '../store/user'
import { useProgressStore } from '../store/progress'
import { useTheme } from '../composables/useTheme'
import { 
  School, Odometer, User, Collection, Document, UserFilled,
  Refresh, SwitchButton, Expand, Fold, WarningFilled, 
  CircleCheckFilled, ArrowUp, Sunny, Moon
} from '@element-plus/icons-vue'

const route = useRoute()
const userStore = useUserStore()
const progressStore = useProgressStore()
const { toggleTheme, getCurrentTheme } = useTheme()

const isCollapsed = ref(false)
const isMobile = ref(false)

const currentRoute = computed(() => route.path)

const currentPageTitle = computed(() => {
  const titleMap = {
    '/dashboard': '仪表盘',
    '/my-courses': '我的课程',
    '/course-library': '课程库',
    '/audit': '毕业审核'
  }
  return titleMap[route.path] || '未知页面'
})

const warningCount = computed(() => {
  return progressStore.auditReport?.warnings?.length || 0
})

const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const refreshData = () => {
  progressStore.fetchAuditReport()
}

// 检测屏幕尺寸
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
  if (isMobile.value) {
    isCollapsed.value = true
  }
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

// 监听路由变化，移动端自动收起侧边栏
watch(route, () => {
  if (isMobile.value) {
    isCollapsed.value = true
  }
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.sidebar {
  width: 260px;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 1000;
}

.sidebar.collapsed {
  width: 72px;
}

.sidebar.mobile {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.mobile:not(.collapsed) {
  transform: translateX(0);
}

.sidebar-header {
  height: 72px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  user-select: none;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 100%;
}

.logo:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.logo-text {
  margin: 0;
  color: white;
  font-size: 20px;
  font-weight: 700;
  white-space: nowrap;
  background: linear-gradient(135deg, #ffffff, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.menu-container {
  flex: 1;
  padding: 16px 12px;
  overflow-y: auto;
}

.sidebar-menu {
  background: transparent;
  border: none;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.user-profile:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.user-profile.collapsed {
  justify-content: center;
  padding: 12px;
}

.user-avatar {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.user-info {
  color: white;
  line-height: 1.3;
  flex: 1;
}

.username {
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 2px;
}

.user-role {
  font-size: 12px;
  color: #94a3b8;
}

.dropdown-icon {
  color: #94a3b8;
  transition: transform 0.3s ease;
}

.user-profile:hover .dropdown-icon {
  transform: translateY(-2px);
}

.sidebar-footer {
  padding: 16px 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.main-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.04);
  z-index: 100;
  height: 72px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.collapse-btn {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.collapse-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.breadcrumb {
  font-size: 15px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-btn {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.header-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.warning-badge {
  cursor: pointer;
}

.main-content {
  padding: 32px;
  background: transparent;
  overflow-y: auto;
  flex: 1;
}

.page-content {
  max-width: 1400px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: calc(100vh - 176px);
  position: relative;
  overflow: hidden;
}

.page-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
}

/* 动画效果 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(-10px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.page-fade-enter-active,
.page-fade-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 菜单样式覆盖 */
:deep(.el-menu) {
  background-color: transparent !important;
  border: none !important;
}

:deep(.el-menu-item) {
  color: #94a3b8 !important;
  border-radius: 10px !important;
  margin: 4px 0 !important;
  border: 1px solid transparent !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

:deep(.el-menu-item::before) {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

:deep(.el-menu-item:hover::before) {
  left: 100%;
}

:deep(.el-menu-item:hover) {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
  transform: translateX(4px) !important;
}

:deep(.el-menu-item.is-active) {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
  color: white !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
}

:deep(.el-menu-item .menu-icon) {
  margin-right: 12px !important;
  font-size: 18px !important;
}

:deep(.el-menu-item .menu-title) {
  font-weight: 500 !important;
  font-size: 14px !important;
}

/* 下拉菜单样式 */
:deep(.user-dropdown) {
  border-radius: 12px !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(20px) !important;
}

:deep(.dropdown-item) {
  padding: 12px 16px !important;
  border-radius: 8px !important;
  margin: 4px 8px !important;
  transition: all 0.3s ease !important;
}

:deep(.dropdown-item:hover) {
  background: #f3f4f6 !important;
  transform: translateX(2px) !important;
}

:deep(.dropdown-item.logout:hover) {
  background: #fef2f2 !important;
  color: #dc2626 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-header {
    padding: 0 20px;
  }
  
  .main-content {
    padding: 20px;
  }
  
  .page-content {
    padding: 20px;
    margin: 0;
    border-radius: 12px;
  }
  
  .header-left {
    gap: 12px;
  }
  
  .header-right {
    gap: 12px;
  }
  
  .breadcrumb {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 16px;
  }
  
  .page-content {
    padding: 16px;
  }
}
</style>