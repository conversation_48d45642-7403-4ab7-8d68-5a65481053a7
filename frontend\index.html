<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="学程助手 - 智能化的学业管理平台，帮助学生轻松管理课程和学分。" />
    <meta name="keywords" content="学程助手,课程管理,学分追踪,毕业审核,学业规划" />
    <title>学程助手 - 智能学业管理平台</title>
    
    <!-- 预加载关键字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 主题色 -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="msapplication-TileColor" content="#3b82f6" />
    
    <!-- 优化SEO -->
    <meta property="og:title" content="学程助手 - 智能学业管理平台" />
    <meta property="og:description" content="智能化的学业管理平台，帮助学生轻松管理课程和学分。" />
    <meta property="og:type" content="website" />
    
    <style>
      /* 防止页面闪烁 */
      #app {
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      
      #app.loaded {
        opacity: 1;
      }
      
      /* 加载动画 */
      .loading-splash {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
      }
      
      .loading-content {
        text-align: center;
        color: white;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
      }
      
      .loading-subtitle {
        font-size: 14px;
        opacity: 0.8;
      }
    </style>
  </head>
  <body>
    <!-- 启动画面 -->
    <div id="loading-splash" class="loading-splash">
      <div class="loading-content">
        <div class="loading-logo"></div>
        <div class="loading-text">学程助手</div>
        <div class="loading-subtitle">正在加载中...</div>
      </div>
    </div>
    
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
    
    <script>
      // 页面加载完成后隐藏启动画面
      window.addEventListener('load', () => {
        setTimeout(() => {
          const splash = document.getElementById('loading-splash');
          const app = document.getElementById('app');
          
          if (splash && app) {
            splash.style.opacity = '0';
            app.classList.add('loaded');
            
            setTimeout(() => {
              splash.remove();
            }, 500);
          }
        }, 1000); // 至少显示1秒
      });
    </script>
  </body>
</html> 