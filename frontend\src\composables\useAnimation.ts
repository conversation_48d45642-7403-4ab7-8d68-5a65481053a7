import { ref, nextTick, readonly } from 'vue'

export interface AnimationOptions {
  duration?: number
  delay?: number
  easing?: string
}

export function useAnimation() {
  const isAnimating = ref(false)
  
  // 淡入动画
  const fadeIn = (element: HTMLElement, options: AnimationOptions = {}) => {
    const { duration = 300, delay = 0, easing = 'ease-in-out' } = options
    
    element.style.opacity = '0'
    element.style.transition = `opacity ${duration}ms ${easing} ${delay}ms`
    
    requestAnimationFrame(() => {
      element.style.opacity = '1'
    })
  }
  
  // 滑入动画
  const slideIn = (element: HTMLElement, direction: 'up' | 'down' | 'left' | 'right' = 'up', options: AnimationOptions = {}) => {
    const { duration = 300, delay = 0, easing = 'ease-out' } = options
    
    const transforms = {
      up: 'translateY(20px)',
      down: 'translateY(-20px)',
      left: 'translateX(20px)',
      right: 'translateX(-20px)'
    }
    
    element.style.opacity = '0'
    element.style.transform = transforms[direction]
    element.style.transition = `all ${duration}ms ${easing} ${delay}ms`
    
    requestAnimationFrame(() => {
      element.style.opacity = '1'
      element.style.transform = 'translate(0, 0)'
    })
  }
  
  // 缩放动画
  const scaleIn = (element: HTMLElement, options: AnimationOptions = {}) => {
    const { duration = 300, delay = 0, easing = 'ease-out' } = options
    
    element.style.opacity = '0'
    element.style.transform = 'scale(0.9)'
    element.style.transition = `all ${duration}ms ${easing} ${delay}ms`
    
    requestAnimationFrame(() => {
      element.style.opacity = '1'
      element.style.transform = 'scale(1)'
    })
  }
  
  // 数字动画
  const animateNumber = (
    from: number,
    to: number,
    duration: number = 1000,
    callback: (value: number) => void
  ) => {
    const startTime = performance.now()
    
    const animate = (currentTime: number) => {
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      // 使用缓动函数
      const easeOutQuart = 1 - Math.pow(1 - progress, 4)
      const currentValue = from + (to - from) * easeOutQuart
      
      callback(Math.round(currentValue))
      
      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    
    requestAnimationFrame(animate)
  }
  
  // 页面进入动画
  const pageEnter = (element: HTMLElement) => {
    const children = Array.from(element.children) as HTMLElement[]
    
    children.forEach((child, index) => {
      child.style.opacity = '0'
      child.style.transform = 'translateY(20px)'
      
      setTimeout(() => {
        child.style.transition = 'all 0.6s ease-out'
        child.style.opacity = '1'
        child.style.transform = 'translateY(0)'
      }, index * 100)
    })
  }
  
  // 卡片悬停动画
  const cardHover = (element: HTMLElement, isHovering: boolean) => {
    element.style.transition = 'all 0.3s ease'
    
    if (isHovering) {
      element.style.transform = 'translateY(-4px) scale(1.02)'
      element.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)'
    } else {
      element.style.transform = 'translateY(0) scale(1)'
      element.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)'
    }
  }
  
  // 加载动画
  const showLoading = async (element: HTMLElement, text: string = '加载中...') => {
    isAnimating.value = true
    
    const loadingDiv = document.createElement('div')
    loadingDiv.className = 'loading-overlay'
    loadingDiv.innerHTML = `
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">${text}</div>
      </div>
    `
    
    element.style.position = 'relative'
    element.appendChild(loadingDiv)
    
    await nextTick()
    fadeIn(loadingDiv)
  }
  
  const hideLoading = (element: HTMLElement) => {
    const loadingDiv = element.querySelector('.loading-overlay') as HTMLElement
    if (loadingDiv) {
      loadingDiv.style.transition = 'opacity 0.3s ease-out'
      loadingDiv.style.opacity = '0'
      
      setTimeout(() => {
        element.removeChild(loadingDiv)
        isAnimating.value = false
      }, 300)
    }
  }
  
  return {
    isAnimating: readonly(isAnimating),
    fadeIn,
    slideIn,
    scaleIn,
    animateNumber,
    pageEnter,
    cardHover,
    showLoading,
    hideLoading,
  }
}
