from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator

# 模块一：课程核心模型
# --------------------

class Course(models.Model):
    # 学分归类枚举，严格对应培养方案的8大分类
    CREDIT_CATEGORY_CHOICES = [
        ('通识必修', '通识教育必修'), ('通识选修', '通识教育选修'),
        ('学科基础', '学科基础课程'), ('专业必修', '专业必修课程'),
        ('专业选修-模块', '专业方向模块选修'), ('专业选修-任选', '专业任选课程'),
        ('实践环节', '集中实践环节'), ('创新创业', '课外创新创业'),
        ('素质拓展', '素质拓展'),
    ]

    # 专业方向模块枚举
    MODULE_CHOICES = [
        ('无', '不属于任何模块'),
        ('企业应用', '企业应用软件方向'), ('智能软件', '智能软件方向'),
        ('云数据', '云数据服务方向'), ('开源应用', '开源应用软件方向'),
    ]

    course_code = models.CharField("课程代码", max_length=20, primary_key=True)
    name = models.CharField("课程名称", max_length=100)
    english_name = models.CharField("英文名称", max_length=200, null=True, blank=True)
    credits = models.FloatField("学分", validators=[MinValueValidator(0.0)])
    credit_category = models.CharField("学分归类", max_length=50, choices=CREDIT_CATEGORY_CHOICES)
    module_name = models.CharField("所属专业模块", max_length=50, choices=MODULE_CHOICES, default='无')
    total_hours = models.IntegerField("总学时", validators=[MinValueValidator(0)])
    theory_hours = models.IntegerField("理论学时", validators=[MinValueValidator(0)])
    practice_hours = models.IntegerField("实践学时", validators=[MinValueValidator(0)])
    recommended_semester = models.IntegerField("建议开课学期", validators=[MinValueValidator(1), MaxValueValidator(8)], null=True, blank=True)
    
    def __str__(self):
        return f"{self.course_code} - {self.name}"

# 模块二：学生与修读记录
# --------------------

class StudentProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="profile")
    # 不再直接关联Course，而是通过一个中间表来记录更详细的信息
    
    def __str__(self):
        return self.user.username

class StudentCourseRecord(models.Model):
    # 修读状态
    STATUS_CHOICES = [
        ('completed', '已修完'),
        ('taking', '在读中'),
        ('planned', '计划修'),
    ]
    student_profile = models.ForeignKey(StudentProfile, on_delete=models.CASCADE, related_name="course_records")
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name="student_records")
    status = models.CharField("修读状态", max_length=20, choices=STATUS_CHOICES, default='planned')
    score = models.FloatField("最终成绩", null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(100)])
    
    class Meta:
        unique_together = ('student_profile', 'course') # 每个学生对一门课只有一条记录

    def __str__(self):
        return f"{self.student_profile.user.username} - {self.course.name} ({self.get_status_display()})"
