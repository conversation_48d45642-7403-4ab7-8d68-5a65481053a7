/* 导入变量 */
@import './variables.scss';

/* Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500&display=swap');

/* TailwindCSS */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-sans);
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: color var(--transition-base), background-color var(--transition-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--radius-lg);
  transition: background-color var(--transition-base);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-dark);
}

/* 通用类 */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.gradient-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.gradient-success {
  background: linear-gradient(135deg, var(--success-color), #059669);
}

.gradient-warning {
  background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.gradient-error {
  background: linear-gradient(135deg, var(--error-color), #dc2626);
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  from { box-shadow: 0 0 5px var(--primary-color); }
  to { box-shadow: 0 0 20px var(--primary-color); }
}

/* 响应式工具类 */
.container-responsive {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

@media (min-width: 768px) {
  .container-responsive {
    padding: 0 var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding: 0 var(--spacing-xl);
  }
}

/* 卡片样式 */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-glass {
  @extend .glass;
  border-radius: var(--radius-xl);
}

/* 按钮增强 */
.btn-glass {
  @extend .glass;
  border: 1px solid var(--glass-border);
  transition: all var(--transition-base);
}

.btn-glass:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 输入框增强 */
.input-glass {
  @extend .glass;
  border: 1px solid var(--glass-border);
  transition: all var(--transition-base);
}

.input-glass:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 加载动画 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 页面过渡 */
.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.3s ease;
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* Element Plus 样式覆盖 */
.el-button {
  border-radius: var(--radius-lg) !important;
  font-weight: 500 !important;
  transition: all var(--transition-base) !important;
}

.el-button:hover {
  transform: translateY(-1px) !important;
}

.el-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-md) !important;
  transition: all var(--transition-base) !important;
}

.el-card:hover {
  box-shadow: var(--shadow-lg) !important;
}

.el-input__wrapper {
  border-radius: var(--radius-lg) !important;
  transition: all var(--transition-base) !important;
}

.el-menu-item {
  border-radius: var(--radius-lg) !important;
  margin: 4px 0 !important;
  transition: all var(--transition-base) !important;
}

/* 深色模式适配 */
[data-theme="dark"] {
  .el-card {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-light) !important;
  }
  
  .el-input__wrapper {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-light) !important;
  }
  
  .el-menu {
    background-color: transparent !important;
  }
}
