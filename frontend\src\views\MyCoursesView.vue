<template>
  <div class="my-courses">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon class="title-icon"><User /></el-icon>
            我的课程
          </h1>
          <p class="page-subtitle">管理您的学习进度，追踪课程完成情况</p>
        </div>
        
        <div class="header-actions">
          <el-button 
            type="primary" 
            @click="showAddCourseDialog = true"
            class="add-course-btn"
          >
            <el-icon><Plus /></el-icon>
            添加课程
          </el-button>
        </div>
      </div>
      
      <!-- 快速统计 -->
      <div class="quick-stats">
        <StatCard
          v-for="(stat, index) in quickStats"
          :key="stat.label"
          :label="stat.label"
          :value="stat.value"
          :icon="stat.icon"
          :variant="stat.variant"
          :suffix="stat.suffix"
          :progress="stat.progress"
          class="quick-stat-card"
          :style="{ animationDelay: `${index * 0.1}s` }"
        />
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filters-section">
      <GlassCard class="filters-card">
        <div class="filters-content">
          <div class="search-bar">
            <el-input
              v-model="searchQuery"
              placeholder="搜索课程名称或代码..."
              :prefix-icon="Search"
              clearable
              class="search-input"
            />
          </div>
          
          <div class="filter-tabs">
            <el-radio-group v-model="activeFilter" class="filter-group">
              <el-radio-button 
                v-for="filter in filterOptions" 
                :key="filter.value"
                :label="filter.value"
                class="filter-btn"
              >
                <el-icon class="filter-icon">
                  <component :is="filter.icon" />
                </el-icon>
                {{ filter.label }}
                <el-badge 
                  v-if="filter.count" 
                  :value="filter.count" 
                  :type="filter.badgeType"
                  class="filter-badge"
                />
              </el-radio-button>
            </el-radio-group>
          </div>
          
          <div class="advanced-filters">
            <el-select
              v-model="selectedCategory"
              placeholder="课程类别"
              clearable
              class="category-select"
            >
              <el-option
                v-for="category in categories"
                :key="category.value"
                :label="category.label"
                :value="category.value"
              />
            </el-select>
            
            <el-select
              v-model="selectedSemester"
              placeholder="学期"
              clearable
              class="semester-select"
            >
              <el-option
                v-for="semester in semesters"
                :key="semester.value"
                :label="semester.label"
                :value="semester.value"
              />
            </el-select>
          </div>
        </div>
      </GlassCard>
    </div>

    <!-- 课程列表 -->
    <div class="courses-section">
      <div class="section-header">
        <h2 class="section-title">课程列表</h2>
        <div class="view-controls">
          <el-radio-group v-model="viewMode" class="view-mode-group">
            <el-radio-button label="grid">
              <el-icon><Grid /></el-icon>
            </el-radio-button>
            <el-radio-button label="list">
              <el-icon><List /></el-icon>
            </el-radio-button>
          </el-radio-group>
          
          <el-dropdown @command="handleSort">
            <el-button class="sort-btn">
              <el-icon><Sort /></el-icon>
              排序
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="name">按名称</el-dropdown-item>
                <el-dropdown-item command="credits">按学分</el-dropdown-item>
                <el-dropdown-item command="status">按状态</el-dropdown-item>
                <el-dropdown-item command="semester">按学期</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 网格视图 -->
      <div 
        v-if="viewMode === 'grid'" 
        class="courses-grid"
        v-loading="isLoading"
      >
        <div
          v-for="(course, index) in filteredCourses"
          :key="course.id"
          class="course-item animate__animated animate__fadeInUp"
          :style="{ animationDelay: `${index * 0.05}s` }"
        >
          <CourseCard
            :course="course"
            @enroll="handleEnrollCourse"
            @view-progress="handleViewProgress"
            @view-details="handleViewDetails"
          />
        </div>
      </div>
      
      <!-- 列表视图 -->
      <div 
        v-else 
        class="courses-list"
        v-loading="isLoading"
      >
        <div
          v-for="(course, index) in filteredCourses"
          :key="course.id"
          class="list-item animate__animated animate__fadeInLeft"
          :style="{ animationDelay: `${index * 0.03}s` }"
        >
          <GlassCard hoverable class="list-course-card">
            <div class="list-course-content">
              <div class="course-main-info">
                <div class="course-status-indicator" :class="getStatusClass(course)"></div>
                <div class="course-details">
                  <h3 class="course-name">{{ course.name }}</h3>
                  <div class="course-meta">
                    <span class="course-code">{{ course.code }}</span>
                    <el-tag size="small" :type="getCategoryTagType(course.category)">
                      {{ course.category }}
                    </el-tag>
                    <span class="course-credits">{{ course.credits }} 学分</span>
                  </div>
                </div>
              </div>
              
              <div class="course-progress-info">
                <div v-if="course.completed" class="completion-status">
                  <el-icon class="completion-icon"><CircleCheckFilled /></el-icon>
                  <span class="completion-text">已完成</span>
                  <div v-if="course.grade" class="course-grade">{{ course.grade }}</div>
                </div>
                
                <div v-else-if="course.inProgress" class="progress-status">
                  <ProgressRing 
                    :percentage="course.progressPercentage || 0" 
                    :size="40"
                    :color="getProgressColor(course.progressPercentage || 0)"
                  />
                  <span class="progress-text">进行中</span>
                </div>
                
                <div v-else class="not-started-status">
                  <el-icon class="pending-icon"><Clock /></el-icon>
                  <span class="pending-text">未开始</span>
                </div>
              </div>
              
              <div class="course-actions">
                <el-button 
                  v-if="!course.completed && !course.inProgress"
                  type="primary"
                  size="small"
                  @click="handleEnrollCourse(course)"
                >
                  开始学习
                </el-button>
                
                <el-button 
                  v-if="course.inProgress"
                  type="success"
                  size="small"
                  @click="handleViewProgress(course)"
                >
                  继续学习
                </el-button>
                
                <el-button 
                  size="small"
                  @click="handleViewDetails(course)"
                >
                  查看详情
                </el-button>
                
                <el-dropdown @command="(command) => handleCourseAction(command, course)">
                  <el-button size="small" circle>
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit">编辑</el-dropdown-item>
                      <el-dropdown-item command="duplicate">复制</el-dropdown-item>
                      <el-dropdown-item command="export">导出</el-dropdown-item>
                      <el-dropdown-item divided command="delete" class="danger-item">
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </GlassCard>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="!isLoading && filteredCourses.length === 0" class="empty-state">
        <div class="empty-content">
          <el-icon class="empty-icon" size="80"><DocumentRemove /></el-icon>
          <h3>暂无课程</h3>
          <p v-if="hasActiveFilters">
            没有找到符合筛选条件的课程，请调整筛选条件或
            <el-button type="primary" link @click="resetFilters">重置筛选</el-button>
          </p>
          <p v-else>
            您还没有添加任何课程，
            <el-button type="primary" link @click="showAddCourseDialog = true">
              立即添加
            </el-button>
          </p>
        </div>
      </div>
    </div>

    <!-- 添加课程对话框 -->
    <el-dialog
      v-model="showAddCourseDialog"
      title="添加课程"
      width="600px"
      class="add-course-dialog"
    >
      <el-form
        ref="addCourseFormRef"
        :model="newCourse"
        :rules="courseRules"
        label-width="80px"
      >
        <el-form-item label="课程名称" prop="name">
          <el-input v-model="newCourse.name" placeholder="请输入课程名称" />
        </el-form-item>
        
        <el-form-item label="课程代码" prop="code">
          <el-input v-model="newCourse.code" placeholder="请输入课程代码" />
        </el-form-item>
        
        <el-form-item label="学分" prop="credits">
          <el-input-number 
            v-model="newCourse.credits" 
            :min="0.5" 
            :max="10" 
            :step="0.5"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="课程类别" prop="category">
          <el-select v-model="newCourse.category" style="width: 100%">
            <el-option
              v-for="category in categories"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="学期" prop="semester">
          <el-select v-model="newCourse.semester" style="width: 100%">
            <el-option
              v-for="semester in semesters"
              :key="semester.value"
              :label="semester.label"
              :value="semester.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="课程描述">
          <el-input
            v-model="newCourse.description"
            type="textarea"
            :rows="3"
            placeholder="请输入课程描述（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddCourseDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAddCourse">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User, Plus, Search, Grid, List, Sort, MoreFilled,
  CircleCheckFilled, Clock, DocumentRemove, Filter
} from '@element-plus/icons-vue'
import GlassCard from '../components/GlassCard.vue'
import StatCard from '../components/StatCard.vue'
import CourseCard from '../components/CourseCard.vue'
import ProgressRing from '../components/ProgressRing.vue'

// 响应式数据
const isLoading = ref(false)
const showAddCourseDialog = ref(false)
const searchQuery = ref('')
const activeFilter = ref('all')
const selectedCategory = ref('')
const selectedSemester = ref('')
const viewMode = ref('grid')

// 表单相关
const addCourseFormRef = ref()
const newCourse = reactive({
  name: '',
  code: '',
  credits: 1,
  category: '',
  semester: '',
  description: ''
})

const courseRules = {
  name: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入课程代码', trigger: 'blur' }],
  credits: [{ required: true, message: '请输入学分', trigger: 'blur' }],
  category: [{ required: true, message: '请选择课程类别', trigger: 'change' }],
  semester: [{ required: true, message: '请选择学期', trigger: 'change' }]
}

// 模拟数据
const courses = ref([
  {
    id: 1,
    name: '高等数学A',
    code: 'MATH101',
    credits: 4,
    category: '基础必修',
    semester: '2024-1',
    completed: true,
    grade: 'A',
    description: '微积分基础课程',
    tags: ['必修', '基础']
  },
  {
    id: 2,
    name: '数据结构',
    code: 'CS201',
    credits: 3,
    category: '专业必修',
    semester: '2024-1',
    inProgress: true,
    progressPercentage: 65,
    description: '计算机科学核心课程',
    tags: ['必修', '专业']
  },
  {
    id: 3,
    name: '软件工程',
    code: 'SE301',
    credits: 3,
    category: '专业必修',
    semester: '2024-2',
    completed: false,
    description: '软件开发方法论',
    tags: ['必修', '专业']
  }
])

// 筛选选项
const filterOptions = computed(() => [
  { 
    value: 'all', 
    label: '全部', 
    icon: 'Grid',
    count: courses.value.length,
    badgeType: 'info'
  },
  { 
    value: 'completed', 
    label: '已完成', 
    icon: 'CircleCheckFilled',
    count: courses.value.filter(c => c.completed).length,
    badgeType: 'success'
  },
  { 
    value: 'inProgress', 
    label: '进行中', 
    icon: 'Clock',
    count: courses.value.filter(c => c.inProgress).length,
    badgeType: 'warning'
  },
  { 
    value: 'notStarted', 
    label: '未开始', 
    icon: 'Plus',
    count: courses.value.filter(c => !c.completed && !c.inProgress).length,
    badgeType: 'info'
  }
])

const categories = ref([
  { value: '基础必修', label: '基础必修' },
  { value: '专业必修', label: '专业必修' },
  { value: '专业选修', label: '专业选修' },
  { value: '通识选修', label: '通识选修' }
])

const semesters = ref([
  { value: '2024-1', label: '2024年春季学期' },
  { value: '2024-2', label: '2024年秋季学期' },
  { value: '2025-1', label: '2025年春季学期' }
])

// 计算属性
const quickStats = computed(() => [
  {
    label: '总课程',
    value: courses.value.length,
    icon: 'Grid',
    variant: 'primary',
    suffix: ' 门'
  },
  {
    label: '已完成',
    value: courses.value.filter(c => c.completed).length,
    icon: 'CircleCheckFilled',
    variant: 'success',
    suffix: ' 门',
    progress: Math.round((courses.value.filter(c => c.completed).length / courses.value.length) * 100)
  },
  {
    label: '进行中',
    value: courses.value.filter(c => c.inProgress).length,
    icon: 'Clock',
    variant: 'warning',
    suffix: ' 门'
  },
  {
    label: '总学分',
    value: courses.value.reduce((sum, c) => sum + c.credits, 0),
    icon: 'Trophy',
    variant: 'default',
    suffix: ' 分'
  }
])

const filteredCourses = computed(() => {
  let filtered = courses.value

  // 按状态筛选
  if (activeFilter.value === 'completed') {
    filtered = filtered.filter(c => c.completed)
  } else if (activeFilter.value === 'inProgress') {
    filtered = filtered.filter(c => c.inProgress)
  } else if (activeFilter.value === 'notStarted') {
    filtered = filtered.filter(c => !c.completed && !c.inProgress)
  }

  // 按类别筛选
  if (selectedCategory.value) {
    filtered = filtered.filter(c => c.category === selectedCategory.value)
  }

  // 按学期筛选
  if (selectedSemester.value) {
    filtered = filtered.filter(c => c.semester === selectedSemester.value)
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(c => 
      c.name.toLowerCase().includes(query) || 
      c.code.toLowerCase().includes(query)
    )
  }

  return filtered
})

const hasActiveFilters = computed(() => {
  return activeFilter.value !== 'all' || 
         selectedCategory.value !== '' || 
         selectedSemester.value !== '' || 
         searchQuery.value !== ''
})

// 方法
const resetFilters = () => {
  activeFilter.value = 'all'
  selectedCategory.value = ''
  selectedSemester.value = ''
  searchQuery.value = ''
}

const getStatusClass = (course: any) => {
  if (course.completed) return 'completed'
  if (course.inProgress) return 'in-progress'
  return 'not-started'
}

const getCategoryTagType = (category: string) => {
  const types: Record<string, string> = {
    '基础必修': 'danger',
    '专业必修': 'warning',
    '专业选修': 'primary',
    '通识选修': 'info'
  }
  return types[category] || 'default'
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#10b981'
  if (percentage >= 60) return '#3b82f6'
  if (percentage >= 40) return '#f59e0b'
  return '#ef4444'
}

const handleSort = (command: string) => {
  // 实现排序逻辑
  console.log('Sort by:', command)
}

const handleEnrollCourse = (course: any) => {
  ElMessage.success(`开始学习：${course.name}`)
}

const handleViewProgress = (course: any) => {
  ElMessage.info(`查看进度：${course.name}`)
}

const handleViewDetails = (course: any) => {
  ElMessage.info(`查看详情：${course.name}`)
}

const handleCourseAction = (command: string, course: any) => {
  ElMessage.info(`${command}: ${course.name}`)
}

const handleAddCourse = async () => {
  if (!addCourseFormRef.value) return
  
  try {
    await addCourseFormRef.value.validate()
    
    // 添加课程逻辑
    courses.value.push({
      id: Date.now(),
      ...newCourse,
      completed: false,
      inProgress: false,
      tags: [newCourse.category.includes('必修') ? '必修' : '选修']
    })
    
    ElMessage.success('课程添加成功！')
    showAddCourseDialog.value = false
    
    // 重置表单
    Object.keys(newCourse).forEach(key => {
      if (key === 'credits') {
        newCourse[key] = 1
      } else {
        newCourse[key] = ''
      }
    })
  } catch (error) {
    console.error('添加课程失败:', error)
  }
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.my-courses {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-icon {
  color: var(--primary-color);
}

.page-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.add-course-btn {
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
}

.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.quick-stat-card {
  animation: fadeInUp 0.6s ease both;
}

.filters-section {
  margin-bottom: 2rem;
}

.filters-card {
  padding: 2rem;
}

.filters-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.search-bar {
  flex: 1;
}

.search-input {
  max-width: 400px;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  padding: 12px 16px;
}

.filter-tabs {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-group {
  display: flex;
  gap: 0.5rem;
}

.filter-btn {
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-icon {
  font-size: 1rem;
}

.filter-badge {
  margin-left: 0.5rem;
}

.advanced-filters {
  display: flex;
  gap: 1rem;
}

.category-select,
.semester-select {
  min-width: 150px;
}

.courses-section {
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.view-mode-group {
  border-radius: 8px;
}

.sort-btn {
  border-radius: 8px;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.course-item {
  animation-fill-mode: both;
}

.courses-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.list-item {
  animation-fill-mode: both;
}

.list-course-card {
  padding: 1.5rem;
}

.list-course-content {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.course-main-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.course-status-indicator {
  width: 8px;
  height: 48px;
  border-radius: 4px;
}

.course-status-indicator.completed {
  background: linear-gradient(180deg, #10b981, #059669);
}

.course-status-indicator.in-progress {
  background: linear-gradient(180deg, #f59e0b, #d97706);
}

.course-status-indicator.not-started {
  background: linear-gradient(180deg, #6b7280, #4b5563);
}

.course-details {
  flex: 1;
}

.course-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.course-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
}

.course-code {
  color: var(--text-secondary);
  font-family: var(--font-mono);
}

.course-credits {
  color: var(--text-tertiary);
}

.course-progress-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  min-width: 120px;
}

.completion-status,
.progress-status,
.not-started-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.completion-status {
  color: #10b981;
}

.progress-status {
  color: #f59e0b;
}

.not-started-status {
  color: #6b7280;
}

.completion-icon,
.pending-icon {
  font-size: 1.25rem;
}

.course-grade {
  padding: 0.25rem 0.5rem;
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 0.5rem;
}

.course-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  color: var(--text-tertiary);
  margin-bottom: 1rem;
}

.empty-content h3 {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.empty-content p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.add-course-dialog :deep(.el-dialog) {
  border-radius: 16px;
}

.dialog-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* 危险操作样式 */
:deep(.danger-item) {
  color: #ef4444 !important;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .courses-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  .list-course-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .course-progress-info {
    justify-content: center;
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .quick-stats {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .filters-content {
    gap: 1rem;
  }
  
  .advanced-filters {
    flex-wrap: wrap;
  }
  
  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .view-controls {
    justify-content: space-between;
  }
  
  .courses-grid {
    grid-template-columns: 1fr;
  }
  
  .course-main-info {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .course-status-indicator {
    width: 100%;
    height: 4px;
  }
}
</style>
