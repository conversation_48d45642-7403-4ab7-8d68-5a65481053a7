### **学程助手 (CourseFlow) - 究极详细版系统设计方案**
产品定位： 一个精确的、个人化的毕业学分核算与规划工具。
核心目标： 帮助学生清晰、准确地追踪每一类毕业要求的学分完成情况，确保在毕业前满足所有量化指标。目前只针对24级软件工程的同学们。

总学分要求： 151 学分
分类学分要求：
通识教育必修：47 学分
通识教育选修：12 学分
学科基础课程：33 学分
专业必修课程：23 学分
专业选修课程：14 学分
集中实践环节：16 学分
课外创新创业：2 学分
素质拓展：4 学分
#### **第一部分：项目准备与环境配置 (Phase 0)**

1.  **技术栈清单 (Finalized):**
    *   **后端:** Python 3.10+, Django 4.2+, Django REST Framework (DRF) 3.14+, PostgreSQL 15+, djangorestframework-simplejwt, django-cors-headers。
    *   **前端:** Node.js 18+, Vue 3.3+, Vite, Vue Router, Pinia, Axios, Element Plus, ECharts。

2.  **开发环境搭建:**
    *   **后端:**
        *   创建 Python 虚拟环境: `python -m venv venv`。
        *   激活虚拟环境并安装依赖: `pip install -r requirements.txt`。
        *   配置本地 `settings.py` 使用 SQLite 以简化初始开发。
        *   运行 `python manage.py migrate` 初始化数据库。
    *   **前端:**
        *   使用 Vite 初始化项目: `npm create vite@latest frontend -- --template vue-ts`。
        *   进入 `frontend` 目录，安装依赖: `npm install`。
        *   配置 `vite.config.ts`，设置代理将 `/api` 请求转发到后端地址 (`http://127.0.0.1:8000`)，解决开发跨域问题。

---

#### **第二部分：数据库与数据模型设计 (The Foundation)**

这是系统的骨架，必须精确无误。

**1. `backend/academic/models.py` - 最终版模型定义:**

```python
from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator

# 模块一：课程核心模型
# --------------------

class Course(models.Model):
    # 学分归类枚举，严格对应培养方案的8大分类
    CREDIT_CATEGORY_CHOICES = [
        ('通识必修', '通识教育必修'), ('通识选修', '通识教育选修'),
        ('学科基础', '学科基础课程'), ('专业必修', '专业必修课程'),
        ('专业选修-模块', '专业方向模块选修'), ('专业选修-任选', '专业任选课程'),
        ('实践环节', '集中实践环节'), ('创新创业', '课外创新创业'),
        ('素质拓展', '素质拓展'),
    ]

    # 专业方向模块枚举
    MODULE_CHOICES = [
        ('无', '不属于任何模块'),
        ('企业应用', '企业应用软件方向'), ('智能软件', '智能软件方向'),
        ('云数据', '云数据服务方向'), ('开源应用', '开源应用软件方向'),
    ]

    course_code = models.CharField("课程代码", max_length=20, primary_key=True)
    name = models.CharField("课程名称", max_length=100)
    english_name = models.CharField("英文名称", max_length=200, null=True, blank=True)
    credits = models.FloatField("学分", validators=[MinValueValidator(0.0)])
    credit_category = models.CharField("学分归类", max_length=50, choices=CREDIT_CATEGORY_CHOICES)
    module_name = models.CharField("所属专业模块", max_length=50, choices=MODULE_CHOICES, default='无')
    total_hours = models.IntegerField("总学时", validators=[MinValueValidator(0)])
    theory_hours = models.IntegerField("理论学时", validators=[MinValueValidator(0)])
    practice_hours = models.IntegerField("实践学时", validators=[MinValueValidator(0)])
    recommended_semester = models.IntegerField("建议开课学期", validators=[MinValueValidator(1), MaxValueValidator(8)])
    
    def __str__(self):
        return f"{self.course_code} - {self.name}"

# 模块二：学生与修读记录
# --------------------

class StudentProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="profile")
    # 不再直接关联Course，而是通过一个中间表来记录更详细的信息
    
    def __str__(self):
        return self.user.username

class StudentCourseRecord(models.Model):
    # 修读状态
    STATUS_CHOICES = [
        ('completed', '已修完'),
        ('taking', '在读中'),
        ('planned', '计划修'),
    ]
    student_profile = models.ForeignKey(StudentProfile, on_delete=models.CASCADE, related_name="course_records")
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name="student_records")
    status = models.CharField("修读状态", max_length=20, choices=STATUS_CHOICES, default='planned')
    score = models.FloatField("最终成绩", null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(100)])
    
    class Meta:
        unique_together = ('student_profile', 'course') # 每个学生对一门课只有一条记录

    def __str__(self):
        return f"{self.student_profile.user.username} - {self.course.name} ({self.get_status_display()})"
```

**2. Django Admin 配置 (`backend/academic/admin.py`):**
*   **目的:** 创建一个“超级数据录入/管理后台”，这是冷启动阶段最重要的工具。
*   **实现:**
    *   使用 `admin.site.register()` 注册上述所有模型。
    *   为 `Course` 模型配置 `list_display`, `search_fields`, `list_filter`，以便能方便地按课程名搜索、按学分归类和建议学期筛选。
    *   为 `StudentProfile` 配置 `inlines`，使其在查看学生档案时能直接编辑其修读课程列表。

---

#### **第三部分：后端 API 设计 (The Engine)**

**1. `Serializers.py` - 数据格式转换器:**
*   `CourseSerializer`: 序列化 `Course` 模型的所有字段。
*   `StudentCourseRecordSerializer`: 序列化学生修读记录。
*   `UserSerializer`: 用于用户注册和信息展示。
*   `LoginSerializer`: 用于用户登录验证。

**2. `Views.py` - API 接口实现:**

*   **`POST /api/auth/register/` (用户注册):**
    *   接收 `username(学号)`, `password`, `email`, `first_name(姓名)`。
    *   创建 `User` 对象和关联的 `StudentProfile` 对象。
    *   成功后返回用户信息和 JWT Token。

*   **`POST /api/auth/login/` (用户登录):**
    *   接收 `username(学号)`, `password`。
    *   验证凭据，成功后返回 JWT Token (包含 `access` 和 `refresh` 两个令牌)。

*   **`GET /api/courses/` (全量课程库):**
    *   返回所有课程的列表。
    *   **支持查询参数:** `?credit_category=学科基础`, `?semester=1`, `?search=数据结构`。
    *   **实现:** 使用 `django-filter` 库可以轻松实现此功能。

*   **`GET /api/me/records/` (获取我的修课记录):**
    *   需要用户认证 (JWT)。
    *   返回当前登录学生的所有 `StudentCourseRecord` 记录。

*   **`POST /api/me/records/` (更新我的修课记录):**
    *   需要用户认证。
    *   **请求体 (Request Body):** `[{ "course_code": "6116038", "status": "completed" }, { "course_code": "6123023", "status": "planned" }]`
    *   **逻辑:** 接收一个课程状态列表，进行批量创建或更新 (`update_or_create`)。这是前端点击勾选框后调用的核心接口。

*   **`GET /api/me/audit/` (核心：毕业审核报告):**
    *   **需要用户认证。这是最复杂的后端逻辑。**
    *   **执行步骤:**
        1.  获取当前登录用户的 `StudentProfile`。
        2.  查询该学生所有 `status='completed'` 的 `StudentCourseRecord` 记录。
        3.  定义毕业要求字典: `requirements = {"总学分": {"required": 151, "completed": 0}, "通识必修": {"required": 47, "completed": 0}, ...}`。
        4.  **遍历已完成课程列表:**
            *   对每门课，将其学分累加到 `requirements['总学分']['completed']`。
            *   根据课程的 `credit_category`，将其学分累加到对应的分类项中，例如 `requirements['学科基础']['completed'] += course.credits`。
        5.  **处理特殊规则 (专业方向模块):**
            *   遍历已完成课程，按 `module_name` 分组。
            *   检查是否有任何一个模块下的所有课程都已被完成。
        6.  **生成预警清单:**
            *   遍历 `requirements` 字典，如果 `completed < required`，则生成一条警告信息。
            *   添加特殊规则的检查结果（如模块未完成）。
        7.  将所有计算结果组装成我们在上一版设计中定义的那个巨大的 JSON 对象，并返回。

---

#### **第四部分：前端设计 (The Cockpit)**

**1. 目录结构 (`frontend/src`):**
```
/src
  /api          # 存放所有调用后端API的函数 (e.g., auth.ts, courses.ts)
  /assets       # 静态资源 (图片, CSS)
  /components   # 可复用的小组件 (e.g., CreditProgressBar.vue, WarningList.vue)
  /router       # 路由配置 (index.ts)
  /store        # Pinia状态管理 (e.g., user.ts, progress.ts)
  /views        # 页面级组件 (e.g., LoginView.vue, DashboardView.vue)
  App.vue
  main.ts
```

**2. Pinia 状态管理 (`store`):**
*   **`user.ts`:**
    *   `state`: `token: string | null`, `userInfo: object | null`。
    *   `actions`: `login(credentials)`, `logout()`, `fetchUserInfo()`。
    *   `getters`: `isAuthenticated: boolean`。
*   **`progress.ts`:**
    *   `state`: `auditReport: object | null`, `isLoading: boolean`。
    *   `actions`: `fetchAuditReport()`，此 action 将调用 `/api/me/audit/` 并更新 state。

**3. 核心页面/组件详解 (`views` and `components`):**

*   **`LoginView.vue`:**
    *   包含一个由 Element Plus 提供的 `ElForm`，有两个输入框（学号、密码）和一个登录按钮。
    *   点击按钮时，调用 `user.ts` store 中的 `login` action。
    *   处理登录成功（跳转到仪表盘）和失败（显示错误提示）的逻辑。

*   **`DashboardView.vue` (主仪表盘):**
    *   **`onMounted` hook:** 在组件挂载时，立即调用 `progress.ts` store 中的 `fetchAuditReport` action。
    *   **模板 (Template):**
        *   使用 `v-if="progressStore.isLoading"` 显示加载动画。
        *   使用 `v-else-if="progressStore.auditReport"` 来渲染整个仪表盘。
        *   **总进度:** 直接绑定 `auditReport.total_credits` 数据到 `ElProgress` 组件。
        *   **分类进度:** 使用 `v-for` 循环遍历 `auditReport.category_details` 数组，为每一项渲染一个带标题和 `ElProgress` 进度条的卡片。
        *   **预警列表:** 将 `auditReport.warnings` 数组传递给一个独立的 `WarningList.vue` 组件进行展示。
        *   **课程列表:** 使用 Element Plus 的 `ElTabs` 组件创建“我的课程”和“课程库”两个标签页。

*   **`MyCoursesTab.vue` (我的课程标签页):**
    *   **核心功能:** 允许学生勾选已完成课程。
    *   **实现:**
        1.  从后端获取全量课程列表 `GET /api/courses/` 和学生已修课程记录 `GET /api/me/records/`。
        2.  使用 `ElTable` 展示所有课程，每行前面有一个 `ElCheckbox`。
        3.  根据已修记录，默认勾选上对应的复选框。
        4.  监听复选框的 `change` 事件。当用户勾选或取消时，将变化的课程信息暂存。
        5.  提供一个“保存更改”按钮，点击后将暂存的变更列表通过 `POST /api/me/records/` 发送到后端。
        6.  保存成功后，再次触发 `progressStore.fetchAuditReport()` 来刷新整个仪表盘。

*   **`CourseLibraryTab.vue` (课程库标签页):**
    *   提供多个 `ElSelect` (下拉筛选框) 和一个 `ElInput` (搜索框)。
    *   根据用户的筛选和搜索条件，动态调用 `GET /api/courses/?...` 接口获取数据。
    *   使用 `ElTable` 展示筛选后的课程数据。