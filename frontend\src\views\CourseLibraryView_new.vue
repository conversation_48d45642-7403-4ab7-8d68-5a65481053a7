<template>
  <div class="course-library">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon class="title-icon"><Collection /></el-icon>
            课程库
          </h1>
          <p class="page-subtitle">探索全部可选课程，规划您的学习路径</p>
        </div>
        
        <div class="header-stats">
          <div class="stat-item">
            <span class="stat-value">{{ totalCourses }}</span>
            <span class="stat-label">总课程</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ availableCourses }}</span>
            <span class="stat-label">可选课程</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ totalCredits }}</span>
            <span class="stat-label">总学分</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-filters-section">
      <GlassCard class="search-card">
        <div class="search-content">
          <!-- 主搜索栏 -->
          <div class="main-search">
            <el-input
              v-model="searchQuery"
              placeholder="搜索课程名称、代码或关键字..."
              :prefix-icon="Search"
              clearable
              size="large"
              class="search-input"
              @input="handleSearch"
            >
              <template #append>
                <el-button type="primary" @click="handleSearch">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </div>
          
          <!-- 快速筛选标签 -->
          <div class="quick-filters">
            <div class="filter-section">
              <span class="filter-label">课程类别：</span>
              <div class="filter-tags">
                <el-tag
                  v-for="category in categories"
                  :key="category.value"
                  :type="selectedCategories.includes(category.value) ? 'primary' : 'info'"
                  :effect="selectedCategories.includes(category.value) ? 'dark' : 'plain'"
                  @click="toggleCategory(category.value)"
                  class="filter-tag"
                  style="cursor: pointer"
                >
                  {{ category.label }}
                  <el-badge
                    v-if="category.count"
                    :value="category.count"
                    class="category-count"
                  />
                </el-tag>
              </div>
            </div>
            
            <div class="filter-section">
              <span class="filter-label">学分范围：</span>
              <div class="filter-tags">
                <el-tag
                  v-for="credit in creditRanges"
                  :key="credit.value"
                  :type="selectedCreditRange === credit.value ? 'primary' : 'info'"
                  :effect="selectedCreditRange === credit.value ? 'dark' : 'plain'"
                  @click="selectedCreditRange = selectedCreditRange === credit.value ? '' : credit.value"
                  class="filter-tag"
                  style="cursor: pointer"
                >
                  {{ credit.label }}
                </el-tag>
              </div>
            </div>
          </div>
          
          <!-- 高级筛选 -->
          <div class="advanced-filters" v-show="showAdvancedFilters">
            <el-row :gutter="16">
              <el-col :span="6">
                <el-select
                  v-model="selectedSemester"
                  placeholder="推荐学期"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="semester in semesters"
                    :key="semester.value"
                    :label="semester.label"
                    :value="semester.value"
                  />
                </el-select>
              </el-col>
              
              <el-col :span="6">
                <el-select
                  v-model="selectedDifficulty"
                  placeholder="难度等级"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="入门" value="beginner" />
                  <el-option label="中级" value="intermediate" />
                  <el-option label="高级" value="advanced" />
                </el-select>
              </el-col>
              
              <el-col :span="6">
                <el-select
                  v-model="selectedStatus"
                  placeholder="课程状态"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="可选" value="available" />
                  <el-option label="已选" value="enrolled" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="前置未满足" value="prerequisite" />
                </el-select>
              </el-col>
              
              <el-col :span="6">
                <el-button @click="resetFilters" style="width: 100%">
                  <el-icon><RefreshLeft /></el-icon>
                  重置筛选
                </el-button>
              </el-col>
            </el-row>
          </div>
          
          <!-- 筛选控制 -->
          <div class="filter-controls">
            <el-button
              type="primary"
              link
              @click="showAdvancedFilters = !showAdvancedFilters"
            >
              <el-icon>
                <ArrowDown v-if="!showAdvancedFilters" />
                <ArrowUp v-else />
              </el-icon>
              {{ showAdvancedFilters ? '收起高级筛选' : '展开高级筛选' }}
            </el-button>
            
            <div class="active-filters" v-if="hasActiveFilters">
              <span class="active-filters-label">当前筛选：</span>
              <el-tag
                v-for="filter in activeFilterTags"
                :key="filter.key"
                closable
                @close="removeFilter(filter.key)"
                class="active-filter-tag"
              >
                {{ filter.label }}
              </el-tag>
            </div>
          </div>
        </div>
      </GlassCard>
    </div>

    <!-- 排序和视图控制 -->
    <div class="controls-section">
      <div class="controls-content">
        <div class="sort-controls">
          <span class="sort-label">排序方式：</span>
          <el-radio-group v-model="sortBy" class="sort-group">
            <el-radio-button label="recommended">推荐</el-radio-button>
            <el-radio-button label="name">课程名称</el-radio-button>
            <el-radio-button label="credits">学分</el-radio-button>
            <el-radio-button label="difficulty">难度</el-radio-button>
          </el-radio-group>
        </div>
        
        <div class="view-controls">
          <el-radio-group v-model="viewMode" class="view-mode-group">
            <el-radio-button label="grid">
              <el-icon><Grid /></el-icon>
            </el-radio-button>
            <el-radio-button label="list">
              <el-icon><List /></el-icon>
            </el-radio-button>
            <el-radio-button label="table">
              <el-icon><Menu /></el-icon>
            </el-radio-button>
          </el-radio-group>
          
          <span class="result-count">
            共找到 <strong>{{ filteredCourses.length }}</strong> 门课程
          </span>
        </div>
      </div>
    </div>

    <!-- 课程展示区域 -->
    <div class="courses-section" v-loading="isLoading">
      <!-- 网格视图 -->
      <div 
        v-if="viewMode === 'grid'" 
        class="courses-grid"
      >
        <div
          v-for="(course, index) in paginatedCourses"
          :key="course.id"
          class="course-item animate__animated animate__fadeInUp"
          :style="{ animationDelay: `${index * 0.05}s` }"
        >
          <CourseCard
            :course="course"
            :featured="course.recommended"
            @enroll="handleEnrollCourse"
            @view-details="handleViewDetails"
          />
        </div>
      </div>
      
      <!-- 列表视图 -->
      <div 
        v-else-if="viewMode === 'list'" 
        class="courses-list"
      >
        <div
          v-for="(course, index) in paginatedCourses"
          :key="course.id"
          class="list-item animate__animated animate__fadeInLeft"
          :style="{ animationDelay: `${index * 0.03}s` }"
        >
          <GlassCard hoverable class="list-course-card">
            <div class="list-course-content">
              <div class="course-main">
                <div class="course-info">
                  <h3 class="course-name">{{ course.name }}</h3>
                  <div class="course-meta">
                    <el-tag size="small" :type="getCategoryTagType(course.category)">
                      {{ course.category }}
                    </el-tag>
                    <span class="course-code">{{ course.code }}</span>
                    <span class="course-credits">{{ course.credits }} 学分</span>
                    <el-rate
                      v-model="course.rating"
                      disabled
                      show-score
                      text-color="#ff9900"
                      score-template="{value}"
                      size="small"
                      class="course-rating"
                    />
                  </div>
                  <p class="course-description">{{ course.description }}</p>
                </div>
                
                <div class="course-status">
                  <div class="difficulty-indicator" :class="getDifficultyClass(course.difficulty)">
                    <span class="difficulty-text">{{ getDifficultyText(course.difficulty) }}</span>
                  </div>
                  
                  <div class="enrollment-info">
                    <div class="enrollment-count">
                      <el-icon><User /></el-icon>
                      <span>{{ course.enrollmentCount || 0 }} 人选课</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="course-actions">
                <el-button
                  v-if="course.available"
                  type="primary"
                  @click="handleEnrollCourse(course)"
                  :disabled="!course.canEnroll"
                >
                  {{ course.enrolled ? '已选课' : '选课' }}
                </el-button>
                
                <el-button
                  v-else
                  disabled
                  type="info"
                >
                  {{ getUnavailableReason(course) }}
                </el-button>
                
                <el-button @click="handleViewDetails(course)">
                  查看详情
                </el-button>
                
                <el-button
                  circle
                  :type="course.bookmarked ? 'warning' : 'default'"
                  @click="toggleBookmark(course)"
                >
                  <el-icon>
                    <StarFilled v-if="course.bookmarked" />
                    <Star v-else />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </GlassCard>
        </div>
      </div>
      
      <!-- 表格视图 -->
      <div v-else class="courses-table">
        <el-table
          :data="paginatedCourses"
          style="width: 100%"
          :row-class-name="getRowClassName"
          @row-click="handleViewDetails"
        >
          <el-table-column prop="name" label="课程名称" min-width="200">
            <template #default="{ row }">
              <div class="table-course-name">
                <span class="name">{{ row.name }}</span>
                <span class="code">{{ row.code }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="category" label="类别" width="120">
            <template #default="{ row }">
              <el-tag size="small" :type="getCategoryTagType(row.category)">
                {{ row.category }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="credits" label="学分" width="80" align="center" />
          
          <el-table-column prop="difficulty" label="难度" width="100" align="center">
            <template #default="{ row }">
              <div class="difficulty-indicator" :class="getDifficultyClass(row.difficulty)">
                {{ getDifficultyText(row.difficulty) }}
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="rating" label="评分" width="120" align="center">
            <template #default="{ row }">
              <el-rate
                v-model="row.rating"
                disabled
                size="small"
                show-score
                text-color="#ff9900"
              />
            </template>
          </el-table-column>
          
          <el-table-column prop="semester" label="推荐学期" width="120" align="center" />
          
          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag
                :type="getStatusTagType(row)"
                size="small"
              >
                {{ getStatusText(row) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="180" align="center" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="row.available && row.canEnroll"
                type="primary"
                size="small"
                @click.stop="handleEnrollCourse(row)"
              >
                选课
              </el-button>
              
              <el-button
                size="small"
                @click.stop="handleViewDetails(row)"
              >
                详情
              </el-button>
              
              <el-button
                circle
                size="small"
                :type="row.bookmarked ? 'warning' : 'default'"
                @click.stop="toggleBookmark(row)"
              >
                <el-icon>
                  <StarFilled v-if="row.bookmarked" />
                  <Star v-else />
                </el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 空状态 -->
      <div v-if="!isLoading && filteredCourses.length === 0" class="empty-state">
        <div class="empty-content">
          <el-icon class="empty-icon" size="80"><DocumentRemove /></el-icon>
          <h3>没有找到课程</h3>
          <p>
            没有找到符合筛选条件的课程，请调整搜索关键字或筛选条件
          </p>
          <el-button type="primary" @click="resetFilters">
            重置筛选条件
          </el-button>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="filteredCourses.length > 0" class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 48, 96]"
        :total="filteredCourses.length"
        layout="total, sizes, prev, pager, next, jumper"
        background
        class="pagination"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Collection, Search, Grid, List, Menu, User, Star, StarFilled,
  ArrowUp, ArrowDown, RefreshLeft, DocumentRemove
} from '@element-plus/icons-vue'
import GlassCard from '../components/GlassCard.vue'
import CourseCard from '../components/CourseCard.vue'

// 响应式数据
const isLoading = ref(false)
const searchQuery = ref('')
const selectedCategories = ref<string[]>([])
const selectedCreditRange = ref('')
const selectedSemester = ref('')
const selectedDifficulty = ref('')
const selectedStatus = ref('')
const showAdvancedFilters = ref(false)
const sortBy = ref('recommended')
const viewMode = ref('grid')
const currentPage = ref(1)
const pageSize = ref(24)

// 模拟数据
const courses = ref([
  {
    id: 1,
    name: '高等数学A',
    code: 'MATH101',
    credits: 4,
    category: '基础必修',
    difficulty: 'intermediate',
    semester: '大一上',
    rating: 4.5,
    enrollmentCount: 245,
    description: '微积分基础课程，为后续数学和专业课程打下坚实基础',
    available: true,
    canEnroll: true,
    enrolled: false,
    completed: false,
    bookmarked: false,
    recommended: true,
    prerequisites: [],
    tags: ['必修', '基础', '数学']
  },
  {
    id: 2,
    name: '数据结构与算法',
    code: 'CS201',
    credits: 3,
    category: '专业必修',
    difficulty: 'advanced',
    semester: '大二上',
    rating: 4.7,
    enrollmentCount: 189,
    description: '计算机科学核心课程，学习各种数据结构和算法设计',
    available: true,
    canEnroll: false,
    enrolled: false,
    completed: false,
    bookmarked: true,
    recommended: true,
    prerequisites: ['CS101'],
    tags: ['必修', '专业', '算法']
  },
  // 更多课程数据...
])

// 筛选选项
const categories = computed(() => [
  { value: '基础必修', label: '基础必修', count: courses.value.filter(c => c.category === '基础必修').length },
  { value: '专业必修', label: '专业必修', count: courses.value.filter(c => c.category === '专业必修').length },
  { value: '专业选修', label: '专业选修', count: courses.value.filter(c => c.category === '专业选修').length },
  { value: '通识选修', label: '通识选修', count: courses.value.filter(c => c.category === '通识选修').length }
])

const creditRanges = ref([
  { value: '1-2', label: '1-2学分' },
  { value: '3-4', label: '3-4学分' },
  { value: '5+', label: '5学分以上' }
])

const semesters = ref([
  { value: '大一上', label: '大一上学期' },
  { value: '大一下', label: '大一下学期' },
  { value: '大二上', label: '大二上学期' },
  { value: '大二下', label: '大二下学期' },
  { value: '大三上', label: '大三上学期' },
  { value: '大三下', label: '大三下学期' },
  { value: '大四上', label: '大四上学期' },
  { value: '大四下', label: '大四下学期' }
])

// 计算属性
const totalCourses = computed(() => courses.value.length)
const availableCourses = computed(() => courses.value.filter(c => c.available).length)
const totalCredits = computed(() => courses.value.reduce((sum, c) => sum + c.credits, 0))

const filteredCourses = computed(() => {
  let filtered = courses.value

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(c => 
      c.name.toLowerCase().includes(query) ||
      c.code.toLowerCase().includes(query) ||
      c.description.toLowerCase().includes(query) ||
      c.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }

  // 类别筛选
  if (selectedCategories.value.length > 0) {
    filtered = filtered.filter(c => selectedCategories.value.includes(c.category))
  }

  // 学分筛选
  if (selectedCreditRange.value) {
    const [min, max] = selectedCreditRange.value === '5+' 
      ? [5, Infinity] 
      : selectedCreditRange.value.split('-').map(Number)
    filtered = filtered.filter(c => c.credits >= min && c.credits <= max)
  }

  // 其他筛选...
  if (selectedSemester.value) {
    filtered = filtered.filter(c => c.semester === selectedSemester.value)
  }

  if (selectedDifficulty.value) {
    filtered = filtered.filter(c => c.difficulty === selectedDifficulty.value)
  }

  if (selectedStatus.value) {
    switch (selectedStatus.value) {
      case 'available':
        filtered = filtered.filter(c => c.available && c.canEnroll)
        break
      case 'enrolled':
        filtered = filtered.filter(c => c.enrolled)
        break
      case 'completed':
        filtered = filtered.filter(c => c.completed)
        break
      case 'prerequisite':
        filtered = filtered.filter(c => !c.canEnroll && c.prerequisites.length > 0)
        break
    }
  }

  // 排序
  switch (sortBy.value) {
    case 'name':
      filtered.sort((a, b) => a.name.localeCompare(b.name))
      break
    case 'credits':
      filtered.sort((a, b) => b.credits - a.credits)
      break
    case 'difficulty':
      const difficultyOrder = { 'beginner': 1, 'intermediate': 2, 'advanced': 3 }
      filtered.sort((a, b) => difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty])
      break
    case 'recommended':
    default:
      filtered.sort((a, b) => {
        if (a.recommended && !b.recommended) return -1
        if (!a.recommended && b.recommended) return 1
        return b.rating - a.rating
      })
      break
  }

  return filtered
})

const paginatedCourses = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredCourses.value.slice(start, end)
})

const hasActiveFilters = computed(() => {
  return searchQuery.value !== '' ||
         selectedCategories.value.length > 0 ||
         selectedCreditRange.value !== '' ||
         selectedSemester.value !== '' ||
         selectedDifficulty.value !== '' ||
         selectedStatus.value !== ''
})

const activeFilterTags = computed(() => {
  const tags = []
  
  if (searchQuery.value) {
    tags.push({ key: 'search', label: `搜索: ${searchQuery.value}` })
  }
  
  selectedCategories.value.forEach(category => {
    tags.push({ key: `category-${category}`, label: `类别: ${category}` })
  })
  
  if (selectedCreditRange.value) {
    tags.push({ key: 'credits', label: `学分: ${selectedCreditRange.value}` })
  }
  
  if (selectedSemester.value) {
    tags.push({ key: 'semester', label: `学期: ${selectedSemester.value}` })
  }
  
  if (selectedDifficulty.value) {
    tags.push({ key: 'difficulty', label: `难度: ${getDifficultyText(selectedDifficulty.value)}` })
  }
  
  if (selectedStatus.value) {
    const statusLabels = {
      'available': '可选',
      'enrolled': '已选',
      'completed': '已完成',
      'prerequisite': '前置未满足'
    }
    tags.push({ key: 'status', label: `状态: ${statusLabels[selectedStatus.value]}` })
  }
  
  return tags
})

// 方法
const toggleCategory = (category: string) => {
  const index = selectedCategories.value.indexOf(category)
  if (index > -1) {
    selectedCategories.value.splice(index, 1)
  } else {
    selectedCategories.value.push(category)
  }
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedCategories.value = []
  selectedCreditRange.value = ''
  selectedSemester.value = ''
  selectedDifficulty.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
}

const removeFilter = (filterKey: string) => {
  if (filterKey === 'search') {
    searchQuery.value = ''
  } else if (filterKey.startsWith('category-')) {
    const category = filterKey.replace('category-', '')
    toggleCategory(category)
  } else if (filterKey === 'credits') {
    selectedCreditRange.value = ''
  } else if (filterKey === 'semester') {
    selectedSemester.value = ''
  } else if (filterKey === 'difficulty') {
    selectedDifficulty.value = ''
  } else if (filterKey === 'status') {
    selectedStatus.value = ''
  }
}

const handleSearch = () => {
  currentPage.value = 1
}

const getCategoryTagType = (category: string) => {
  const types: Record<string, string> = {
    '基础必修': 'danger',
    '专业必修': 'warning',
    '专业选修': 'primary',
    '通识选修': 'info'
  }
  return types[category] || 'default'
}

const getDifficultyClass = (difficulty: string) => {
  return `difficulty-${difficulty}`
}

const getDifficultyText = (difficulty: string) => {
  const texts = {
    'beginner': '入门',
    'intermediate': '中级',
    'advanced': '高级'
  }
  return texts[difficulty] || difficulty
}

const getStatusTagType = (course: any) => {
  if (course.completed) return 'success'
  if (course.enrolled) return 'warning'
  if (course.available && course.canEnroll) return 'primary'
  return 'info'
}

const getStatusText = (course: any) => {
  if (course.completed) return '已完成'
  if (course.enrolled) return '已选课'
  if (course.available && course.canEnroll) return '可选'
  return '不可选'
}

const getUnavailableReason = (course: any) => {
  if (!course.available) return '暂不开放'
  if (!course.canEnroll) return '前置未满足'
  return '不可选'
}

const getRowClassName = ({ row }: any) => {
  if (row.recommended) return 'recommended-row'
  return ''
}

const handleEnrollCourse = (course: any) => {
  if (!course.canEnroll) {
    ElMessage.warning('该课程前置条件未满足')
    return
  }
  
  ElMessage.success(`成功选择课程：${course.name}`)
  course.enrolled = true
}

const handleViewDetails = (course: any) => {
  ElMessage.info(`查看课程详情：${course.name}`)
}

const toggleBookmark = (course: any) => {
  course.bookmarked = !course.bookmarked
  ElMessage.success(course.bookmarked ? '已收藏' : '已取消收藏')
}

// 监听筛选变化，重置分页
watch([searchQuery, selectedCategories, selectedCreditRange, selectedSemester, selectedDifficulty, selectedStatus], () => {
  currentPage.value = 1
}, { deep: true })

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.course-library {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  gap: 2rem;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-icon {
  color: var(--primary-color);
}

.page-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.search-filters-section {
  margin-bottom: 2rem;
}

.search-card {
  padding: 2rem;
}

.search-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.main-search {
  display: flex;
  justify-content: center;
}

.search-input {
  max-width: 600px;
  width: 100%;
}

.search-input :deep(.el-input-group__append) {
  border-radius: 0 12px 12px 0;
}

.quick-filters {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-label {
  font-weight: 600;
  color: var(--text-primary);
  min-width: 80px;
}

.filter-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-tag:hover {
  transform: translateY(-1px);
}

.category-count {
  margin-left: 0.25rem;
}

.advanced-filters {
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}

.active-filters {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.active-filters-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.active-filter-tag {
  font-size: 0.75rem;
}

.controls-section {
  margin-bottom: 1.5rem;
}

.controls-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sort-label {
  font-weight: 600;
  color: var(--text-primary);
}

.sort-group {
  border-radius: 8px;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.view-mode-group {
  border-radius: 8px;
}

.result-count {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.courses-section {
  margin-bottom: 2rem;
  min-height: 400px;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.course-item {
  animation-fill-mode: both;
}

.courses-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.list-item {
  animation-fill-mode: both;
}

.list-course-card {
  padding: 1.5rem;
}

.list-course-content {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.course-main {
  flex: 1;
  display: flex;
  gap: 2rem;
}

.course-info {
  flex: 1;
}

.course-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.course-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
}

.course-code {
  font-family: var(--font-mono);
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.course-credits {
  color: var(--text-tertiary);
  font-size: 0.875rem;
}

.course-rating {
  font-size: 0.875rem;
}

.course-description {
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.course-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  min-width: 120px;
}

.difficulty-indicator {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
}

.difficulty-beginner {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.difficulty-intermediate {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.difficulty-advanced {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.enrollment-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.enrollment-count {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.course-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 120px;
}

.courses-table {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.table-course-name {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.table-course-name .name {
  font-weight: 600;
  color: var(--text-primary);
}

.table-course-name .code {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-family: var(--font-mono);
}

:deep(.recommended-row) {
  background: rgba(59, 130, 246, 0.05) !important;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  color: var(--text-tertiary);
  margin-bottom: 1rem;
}

.empty-content h3 {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.empty-content p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding-top: 2rem;
}

.pagination {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 1rem;
  backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }
  
  .header-stats {
    justify-content: space-around;
  }
  
  .courses-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  .list-course-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .course-main {
    flex-direction: column;
    gap: 1rem;
  }
  
  .course-actions {
    flex-direction: row;
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }
  
  .controls-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .sort-controls,
  .view-controls {
    justify-content: space-between;
  }
  
  .courses-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .filter-label {
    min-width: auto;
  }
}
</style>
