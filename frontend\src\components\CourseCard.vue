<template>
  <GlassCard 
    hoverable 
    class="course-card"
    :class="{ 'is-completed': course.completed, 'is-featured': featured }"
  >
    <div class="course-content">
      <div class="course-header">
        <div class="course-status" :class="statusClass">
          <el-icon class="status-icon">
            <CircleCheckFilled v-if="course.completed" />
            <Clock v-else-if="course.inProgress" />
            <Plus v-else />
          </el-icon>
          <span class="status-text">{{ statusText }}</span>
        </div>
        
        <div v-if="course.grade" class="course-grade" :class="gradeClass">
          {{ course.grade }}
        </div>
      </div>
      
      <div class="course-body">
        <h3 class="course-name">{{ course.name }}</h3>
        <div class="course-code">{{ course.code }}</div>
        
        <div class="course-info">
          <div class="info-item">
            <el-icon class="info-icon"><CreditCard /></el-icon>
            <span>{{ course.credits }} 学分</span>
          </div>
          
          <div v-if="course.semester" class="info-item">
            <el-icon class="info-icon"><Calendar /></el-icon>
            <span>{{ course.semester }}</span>
          </div>
          
          <div v-if="course.category" class="info-item">
            <el-icon class="info-icon"><Collection /></el-icon>
            <span>{{ course.category }}</span>
          </div>
        </div>
        
        <div v-if="course.description" class="course-description">
          {{ course.description }}
        </div>
      </div>
      
      <div class="course-footer">
        <div class="course-tags">
          <el-tag 
            v-for="tag in course.tags" 
            :key="tag"
            size="small"
            class="course-tag"
            :type="getTagType(tag)"
          >
            {{ tag }}
          </el-tag>
        </div>
        
        <div class="course-actions">
          <el-button 
            v-if="!course.completed && !course.inProgress" 
            type="primary" 
            size="small"
            @click="handleEnroll"
            class="action-btn"
          >
            选课
          </el-button>
          
          <el-button 
            v-if="course.inProgress" 
            type="success" 
            size="small"
            @click="handleViewProgress"
            class="action-btn"
          >
            查看进度
          </el-button>
          
          <el-button 
            v-if="course.completed" 
            type="info" 
            size="small"
            @click="handleViewDetails"
            class="action-btn"
          >
            查看详情
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 完成状态装饰 -->
    <div v-if="course.completed" class="completion-decoration">
      <div class="completion-badge">
        <el-icon><Trophy /></el-icon>
      </div>
    </div>
  </GlassCard>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  CircleCheckFilled, Clock, Plus, CreditCard, 
  Calendar, Collection, Trophy 
} from '@element-plus/icons-vue'
import GlassCard from './GlassCard.vue'

interface Course {
  id: string | number
  name: string
  code: string
  credits: number
  completed: boolean
  inProgress?: boolean
  grade?: string
  semester?: string
  category?: string
  description?: string
  tags?: string[]
}

interface Props {
  course: Course
  featured?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  featured: false
})

const emit = defineEmits<{
  enroll: [course: Course]
  viewProgress: [course: Course]
  viewDetails: [course: Course]
}>()

const statusClass = computed(() => {
  if (props.course.completed) return 'completed'
  if (props.course.inProgress) return 'in-progress'
  return 'not-started'
})

const statusText = computed(() => {
  if (props.course.completed) return '已完成'
  if (props.course.inProgress) return '进行中'
  return '未开始'
})

const gradeClass = computed(() => {
  const grade = props.course.grade
  if (!grade) return ''
  
  if (grade >= '90' || grade === 'A') return 'grade-excellent'
  if (grade >= '80' || grade === 'B') return 'grade-good'
  if (grade >= '70' || grade === 'C') return 'grade-fair'
  return 'grade-poor'
})

const getTagType = (tag: string) => {
  const tagTypes: Record<string, string> = {
    '必修': 'danger',
    '选修': 'primary',
    '核心': 'warning',
    '基础': 'info',
    '专业': 'success'
  }
  return tagTypes[tag] || 'default'
}

const handleEnroll = () => {
  emit('enroll', props.course)
}

const handleViewProgress = () => {
  emit('viewProgress', props.course)
}

const handleViewDetails = () => {
  emit('viewDetails', props.course)
}
</script>

<style scoped>
.course-card {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.course-card.is-completed {
  border-color: rgba(16, 185, 129, 0.3);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.1));
}

.course-card.is-featured {
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
}

.course-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(31, 38, 135, 0.25);
}

.course-content {
  padding: 1.5rem;
}

.course-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.course-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.course-status.completed {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.course-status.in-progress {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.course-status.not-started {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.status-icon {
  font-size: 0.875rem;
}

.course-grade {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 700;
}

.grade-excellent {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.grade-good {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.grade-fair {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.grade-poor {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.course-body {
  margin-bottom: 1.5rem;
}

.course-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.course-code {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-family: var(--font-mono);
  margin-bottom: 1rem;
}

.course-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.info-icon {
  font-size: 1rem;
  color: var(--primary-color);
}

.course-description {
  font-size: 0.875rem;
  color: var(--text-tertiary);
  line-height: 1.5;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.course-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.course-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  flex: 1;
}

.course-tag {
  font-size: 0.75rem;
}

.course-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
}

.completion-decoration {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  animation: pulse 2s ease-in-out infinite;
}

.completion-badge .el-icon {
  color: white;
  font-size: 1rem;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .course-content {
    padding: 1.25rem;
  }
  
  .course-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .course-actions {
    justify-content: center;
  }
  
  .course-info {
    flex-direction: column;
  }
}
</style>
