<template>
  <div class="audit-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>毕业审核</h1>
      <p class="page-description">详细查看毕业要求完成情况，确保满足所有毕业条件</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="progressStore.isLoading" class="loading-container">
      <el-skeleton :rows="5" animated />
      <div class="loading-text">
        <el-icon class="is-loading"><Loading /></el-icon>
        正在加载审核数据...
      </div>
    </div>

    <!-- 审核内容 -->
    <div v-else-if="progressStore.auditReport" class="audit-content">
      <!-- 总体状态 -->
      <el-card class="summary-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Trophy /></el-icon>
            <span>毕业要求总览</span>
            <el-tag 
              :type="isGraduationReady ? 'success' : 'warning'" 
              size="large"
            >
              {{ isGraduationReady ? '满足毕业要求' : '未满足毕业要求' }}
            </el-tag>
          </div>
        </template>
        
        <div class="summary-content">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="8">
              <div class="summary-item">
                <div class="summary-value">
                  {{ progressStore.auditReport.total_credits_summary.completed }} / 
                  {{ progressStore.auditReport.total_credits_summary.required }}
                </div>
                <div class="summary-label">总学分</div>
                <el-progress 
                  :percentage="calculatePercentage(progressStore.auditReport.total_credits_summary.completed, progressStore.auditReport.total_credits_summary.required)"
                  :status="getProgressStatus(progressStore.auditReport.total_credits_summary.completed, progressStore.auditReport.total_credits_summary.required)"
                />
              </div>
            </el-col>
            <el-col :xs="24" :sm="8">
              <div class="summary-item">
                <div class="summary-value">{{ completedCategories }} / {{ totalCategories }}</div>
                <div class="summary-label">已完成分类</div>
                <el-progress 
                  :percentage="(completedCategories / totalCategories) * 100"
                  :status="completedCategories === totalCategories ? 'success' : 'exception'"
                />
              </div>
            </el-col>
            <el-col :xs="24" :sm="8">
              <div class="summary-item">
                <div class="summary-value">{{ progressStore.auditReport.warnings.length }}</div>
                <div class="summary-label">预警项目</div>
                <el-progress 
                  :percentage="progressStore.auditReport.warnings.length === 0 ? 100 : 50"
                  :status="progressStore.auditReport.warnings.length === 0 ? 'success' : 'exception'"
                />
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 详细分类要求 -->
      <el-card class="details-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><List /></el-icon>
            <span>分类学分详情</span>
          </div>
        </template>
        
        <div class="categories-grid">
          <div 
            v-for="category in progressStore.auditReport.category_details" 
            :key="category.name"
            class="category-card"
            :class="{ 'completed': category.completed >= category.required }"
          >
            <div class="category-header">
              <h4>{{ category.name }}</h4>
              <el-tag 
                :type="getTagType(category.completed, category.required)"
                size="small"
              >
                {{ category.completed >= category.required ? '已完成' : '未完成' }}
              </el-tag>
            </div>
            
            <div class="category-stats">
              <div class="stats-row">
                <span>已获得学分:</span>
                <strong>{{ category.completed }}</strong>
              </div>
              <div class="stats-row">
                <span>要求学分:</span>
                <strong>{{ category.required }}</strong>
              </div>
              <div class="stats-row">
                <span>差额:</span>
                <strong :class="{ 'text-success': category.completed >= category.required, 'text-danger': category.completed < category.required }">
                  {{ category.completed >= category.required ? '已满足' : `还需 ${category.required - category.completed} 学分` }}
                </strong>
              </div>
            </div>
            
            <el-progress 
              :percentage="calculatePercentage(category.completed, category.required)"
              :status="getProgressStatus(category.completed, category.required)"
              :stroke-width="8"
            />
            
            <!-- 已完成课程列表 -->
            <div v-if="category.details && category.details.length > 0" class="completed-courses">
              <h5>已完成课程 ({{ category.details.length }}门):</h5>
              <div class="course-list">
                <el-tag 
                  v-for="course in category.details.slice(0, 3)" 
                  :key="course.course_code"
                  size="small"
                  class="course-tag"
                >
                  {{ course.name }} ({{ course.credits }}学分)
                </el-tag>
                <el-tag 
                  v-if="category.details.length > 3"
                  type="info"
                  size="small"
                >
                  +{{ category.details.length - 3 }}门课程
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 预警信息 -->
      <el-card v-if="progressStore.auditReport.warnings.length > 0" class="warnings-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><WarningFilled /></el-icon>
            <span>预警信息</span>
            <el-tag type="warning">{{ progressStore.auditReport.warnings.length }} 项</el-tag>
          </div>
        </template>
        
        <div class="warnings-list">
          <el-alert
            v-for="(warning, index) in progressStore.auditReport.warnings"
            :key="index"
            :title="warning"
            type="warning"
            show-icon
            :closable="false"
            class="warning-item"
          />
        </div>
        
        <div class="warning-actions">
          <el-button type="primary" @click="$router.push('/my-courses')">
            <el-icon><User /></el-icon>
            更新课程状态
          </el-button>
          <el-button @click="$router.push('/course-library')">
            <el-icon><Collection /></el-icon>
            查看课程库
          </el-button>
        </div>
      </el-card>

      <!-- 成功状态 -->
      <el-card v-else class="success-card" shadow="hover">
        <el-result
          icon="success"
          title="恭喜您！"
          sub-title="您已满足所有毕业要求"
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/dashboard')">
              <el-icon><House /></el-icon>
              返回仪表盘
            </el-button>
          </template>
        </el-result>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useProgressStore } from '../store/progress'
import { 
  Loading, Trophy, List, WarningFilled, User, Collection, House
} from '@element-plus/icons-vue'

const progressStore = useProgressStore()

const completedCategories = computed(() => {
  if (!progressStore.auditReport?.category_details) return 0
  return progressStore.auditReport.category_details.filter(
    category => category.completed >= category.required
  ).length
})

const totalCategories = computed(() => {
  return progressStore.auditReport?.category_details?.length || 0
})

const isGraduationReady = computed(() => {
  if (!progressStore.auditReport) return false
  const totalCreditsOk = progressStore.auditReport.total_credits_summary.completed >= 
                        progressStore.auditReport.total_credits_summary.required
  const noWarnings = progressStore.auditReport.warnings.length === 0
  return totalCreditsOk && noWarnings
})

onMounted(() => {
  progressStore.fetchAuditReport()
})

const calculatePercentage = (completed: number, required: number) => {
  if (required === 0) return 100
  return Math.min((completed / required) * 100, 100)
}

const getProgressStatus = (completed: number, required: number) => {
  if (required === 0) return 'success'
  const percentage = (completed / required) * 100
  if (percentage >= 100) return 'success'
  if (percentage >= 80) return undefined
  return 'exception'
}

const getTagType = (completed: number, required: number) => {
  if (completed >= required) return 'success'
  if (completed >= required * 0.8) return 'warning'
  return 'danger'
}
</script>

<style scoped>
.audit-page {
  padding: 0;
}

.page-header {
  margin-bottom: 32px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 20px;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: #666;
}

.audit-content {
  space-y: 24px;
}

.summary-card, .details-card, .warnings-card, .success-card {
  border-radius: 12px;
  border: none;
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.card-header > span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-content {
  padding: 16px 0;
}

.summary-item {
  text-align: center;
  margin-bottom: 16px;
}

.summary-value {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.summary-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 12px;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.category-card {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
  background: #fafafa;
}

.category-card.completed {
  border-color: #10b981;
  background: #f0fdf4;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.category-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.category-stats {
  margin-bottom: 16px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.text-success {
  color: #10b981;
}

.text-danger {
  color: #ef4444;
}

.completed-courses {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.completed-courses h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.course-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.course-tag {
  margin-bottom: 4px;
}

.warnings-list {
  margin-bottom: 20px;
}

.warning-item {
  margin-bottom: 12px;
  border-radius: 8px;
}

.warning-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header h1 {
    font-size: 24px;
  }
  
  .categories-grid {
    grid-template-columns: 1fr;
  }
  
  .summary-value {
    font-size: 24px;
  }
  
  .warning-actions {
    flex-direction: column;
  }
}
</style> 