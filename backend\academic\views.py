from django.shortcuts import render
from django.contrib.auth.models import User
from rest_framework import generics, permissions, views, response
from rest_framework_simplejwt.views import TokenObtainPairView
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from .models import Course, StudentCourseRecord, StudentProfile
from .serializers import RegisterSerializer, UserSerializer, CourseSerializer, StudentCourseRecordSerializer

# Create your views here.

# 视图一：用户注册
class RegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    permission_classes = (permissions.AllowAny,)
    serializer_class = RegisterSerializer

# 视图二：用户登录 (使用 simplejwt 默认视图)
# 我们将在 urls.py 中直接使用 TokenObtainPairView

# 视图三：获取所有课程
class CourseListView(generics.ListAPIView):
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    permission_classes = (permissions.IsAuthenticated,)
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['credit_category', 'recommended_semester']
    search_fields = ['name', 'course_code']

# 视图四：获取和更新当前用户的修课记录
class MyRecordsView(views.APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        student_profile = request.user.profile
        records = StudentCourseRecord.objects.filter(student_profile=student_profile)
        serializer = StudentCourseRecordSerializer(records, many=True)
        return response.Response(serializer.data)

    def post(self, request):
        student_profile = request.user.profile
        # 请求体格式: `[{ "course_code": "...", "status": "..." }, ...]`
        records_data = request.data
        if not isinstance(records_data, list):
            return response.Response({"error": "Request body must be a list of records"}, status=400)

        updated_records = []
        for record_data in records_data:
            course_code = record_data.get('course_code')
            status = record_data.get('status')
            
            if not course_code or not status:
                continue

            try:
                course = Course.objects.get(pk=course_code)
                record, created = StudentCourseRecord.objects.update_or_create(
                    student_profile=student_profile,
                    course=course,
                    defaults={'status': status}
                )
                updated_records.append(record)
            except Course.DoesNotExist:
                # 可以选择在这里记录日志或返回错误信息
                continue
        
        serializer = StudentCourseRecordSerializer(updated_records, many=True)
        return response.Response(serializer.data)

# 视图五：获取毕业审核报告
class AuditView(views.APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        student_profile = request.user.profile
        completed_records = StudentCourseRecord.objects.filter(
            student_profile=student_profile,
            status='completed'
        )

        # 1. 定义毕业要求
        requirements = {
            '总学分': {'required': 151, 'completed': 0, 'details': []},
            '通识教育必修': {'required': 47, 'completed': 0, 'details': []},
            '通识教育选修': {'required': 12, 'completed': 0, 'details': []},
            '学科基础课程': {'required': 33, 'completed': 0, 'details': []},
            '专业必修课程': {'required': 23, 'completed': 0, 'details': []},
            '专业选修课程': {'required': 14, 'completed': 0, 'details': []},
            '集中实践环节': {'required': 16, 'completed': 0, 'details': []},
            '课外创新创业': {'required': 2, 'completed': 0, 'details': []},
            '素质拓展': {'required': 4, 'completed': 0, 'details': []},
        }

        # 2. 遍历已完成课程并累加学分
        for record in completed_records:
            course = record.course
            category_map = {
                '通识必修': '通识教育必修',
                '通识选修': '通识教育选修',
                '学科基础': '学科基础课程',
                '专业必修': '专业必修课程',
                '专业选修-模块': '专业选修课程', # 模块选修和任选都归入专业选修
                '专业选修-任选': '专业选修课程',
                '实践环节': '集中实践环节',
                '创新创业': '课外创新创业',
                '素质拓展': '素质拓展',
            }
            major_category = category_map.get(course.credit_category)

            if major_category:
                requirements[major_category]['completed'] += course.credits
                requirements[major_category]['details'].append(CourseSerializer(course).data)
        
        # 计算总学分
        total_completed = sum(cat['completed'] for cat in requirements.values())
        requirements['总学分']['completed'] = total_completed

        # 3. 生成预警清单 (简化版)
        warnings = []
        for category, data in requirements.items():
            if data['completed'] < data['required']:
                warnings.append(f"{category} 未修满，还差 {data['required'] - data['completed']} 学分")

        # 4. 组装最终报告
        report = {
            "total_credits_summary": requirements['总学分'],
            "category_details": [
                {"name": name, **data} for name, data in requirements.items() if name != '总学分'
            ],
            "warnings": warnings,
        }

        return response.Response(report)
