<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="bg-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
      </div>
      <div class="bg-gradient"></div>
    </div>

    <!-- 主要内容 -->
    <div class="login-content">
      <!-- 左侧信息面板 -->
      <div class="info-panel">
        <div class="info-content">
          <div class="logo-section">
            <div class="logo-icon">
              <el-icon size="48" color="#ffffff">
                <School />
              </el-icon>
            </div>
            <h1 class="logo-title">学程助手</h1>
            <p class="logo-subtitle">智能化学业管理平台</p>
          </div>
          
          <div class="features-list">
            <div class="feature-item">
              <el-icon class="feature-icon"><CircleCheckFilled /></el-icon>
              <div class="feature-text">
                <h3>学分追踪</h3>
                <p>实时监控学分完成情况</p>
              </div>
            </div>
            
            <div class="feature-item">
              <el-icon class="feature-icon"><TrendCharts /></el-icon>
              <div class="feature-text">
                <h3>进度分析</h3>
                <p>可视化展示学习进度</p>
              </div>
            </div>
            
            <div class="feature-item">
              <el-icon class="feature-icon"><Document /></el-icon>
              <div class="feature-text">
                <h3>毕业审核</h3>
                <p>智能化毕业条件检查</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-panel">
        <GlassCard class="login-card">
          <div class="login-header">
            <h2>欢迎登录</h2>
            <p>请输入您的学号和密码</p>
          </div>

          <el-form 
            ref="loginFormRef"
            :model="loginForm" 
            :rules="loginRules"
            class="login-form"
            size="large"
            @submit.prevent="handleLogin"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入学号"
                :prefix-icon="User"
                clearable
                class="login-input"
                @keyup.enter="handleLogin"
              />
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                :prefix-icon="Lock"
                show-password
                clearable
                class="login-input"
                @keyup.enter="handleLogin"
              />
            </el-form-item>

            <el-form-item>
              <div class="login-options">
                <el-checkbox v-model="rememberMe" class="remember-checkbox">
                  记住我
                </el-checkbox>
                <el-button type="primary" link class="forgot-password">
                  忘记密码？
                </el-button>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="isLoading"
                @click="handleLogin"
                class="login-button"
                block
              >
                <span v-if="!isLoading">登录</span>
                <span v-else>登录中...</span>
              </el-button>
            </el-form-item>
          </el-form>

          <div class="login-footer">
            <el-divider>
              <span class="divider-text">其他登录方式</span>
            </el-divider>
            
            <div class="social-login">
              <el-button circle class="social-btn wechat">
                <el-icon><ChatDotRound /></el-icon>
              </el-button>
              <el-button circle class="social-btn qq">
                <el-icon><User /></el-icon>
              </el-button>
              <el-button circle class="social-btn github">
                <el-icon><Link /></el-icon>
              </el-button>
            </div>
          </div>
        </GlassCard>

        <!-- 帮助信息 -->
        <div class="help-section">
          <p class="help-text">
            首次使用？
            <el-button type="primary" link class="help-link">
              查看使用指南
            </el-button>
          </p>
          <p class="help-text">
            遇到问题？
            <el-button type="primary" link class="help-link">
              联系技术支持
            </el-button>
          </p>
        </div>
      </div>
    </div>

    <!-- 版权信息 -->
    <div class="copyright">
      <p>&copy; 2024 学程助手. All rights reserved.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../store/user'
import { ElMessage, type FormInstance } from 'element-plus'
import { 
  School, User, Lock, CircleCheckFilled, TrendCharts, 
  Document, ChatDotRound, Link 
} from '@element-plus/icons-vue'
import GlassCard from '../components/GlassCard.vue'

const router = useRouter()
const userStore = useUserStore()

const loginFormRef = ref<FormInstance>()
const isLoading = ref(false)
const rememberMe = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入学号', trigger: 'blur' },
    { min: 6, max: 20, message: '学号长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    isLoading.value = true
    
    // 模拟登录API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 设置用户信息
    userStore.setUserInfo({
      id: 1,
      username: loginForm.username,
      name: '张三',
      email: `${loginForm.username}@student.edu.cn`,
      major: '软件工程',
      grade: '2024级'
    })
    
    ElMessage.success('登录成功！')
    
    // 跳转到仪表盘
    router.replace('/dashboard')
    
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请检查用户名和密码')
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    #667eea 0%, 
    #764ba2 25%, 
    #667eea 50%, 
    #764ba2 75%, 
    #667eea 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.bg-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 20s ease-in-out infinite;
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: 10%;
  animation-delay: -5s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 20%;
  animation-delay: -10s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 30%;
  right: 30%;
  animation-delay: -15s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-30px) rotate(120deg); }
  66% { transform: translateY(30px) rotate(240deg); }
}

.login-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 4rem;
}

.info-panel {
  flex: 1;
  max-width: 500px;
  color: white;
}

.info-content {
  padding: 2rem;
}

.logo-section {
  text-align: center;
  margin-bottom: 3rem;
}

.logo-icon {
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.logo-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.logo-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(8px);
}

.feature-icon {
  font-size: 2rem;
  color: #fbbf24;
}

.feature-text h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.feature-text p {
  margin: 0;
  opacity: 0.8;
  font-size: 0.9rem;
}

.login-panel {
  flex: 1;
  max-width: 450px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.login-card {
  padding: 3rem;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.login-header p {
  color: var(--text-secondary);
  margin: 0;
}

.login-form {
  margin-bottom: 2rem;
}

.login-input {
  margin-bottom: 1rem;
}

.login-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.login-input :deep(.el-input__wrapper):hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.login-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.remember-checkbox {
  color: var(--text-secondary);
}

.forgot-password {
  font-size: 0.9rem;
}

.login-button {
  height: 48px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5);
}

.login-footer {
  text-align: center;
}

.divider-text {
  color: var(--text-tertiary);
  font-size: 0.9rem;
}

.social-login {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;
}

.social-btn {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.social-btn:hover {
  transform: translateY(-2px);
}

.social-btn.wechat {
  background: #07c160;
  border-color: #07c160;
  color: white;
}

.social-btn.qq {
  background: #1296db;
  border-color: #1296db;
  color: white;
}

.social-btn.github {
  background: #333;
  border-color: #333;
  color: white;
}

.help-section {
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
}

.help-text {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.help-link {
  font-size: 0.9rem;
}

.copyright {
  text-align: center;
  padding: 1rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-content {
    flex-direction: column;
    gap: 2rem;
  }
  
  .info-panel {
    max-width: 100%;
  }
  
  .features-list {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .feature-item {
    flex: 1;
    min-width: 250px;
  }
}

@media (max-width: 768px) {
  .login-content {
    padding: 1rem;
  }
  
  .login-card {
    padding: 2rem;
  }
  
  .logo-title {
    font-size: 2.5rem;
  }
  
  .features-list {
    flex-direction: column;
  }
  
  .feature-item {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 1.5rem;
  }
  
  .logo-title {
    font-size: 2rem;
  }
  
  .login-header h2 {
    font-size: 1.5rem;
  }
}
</style>
