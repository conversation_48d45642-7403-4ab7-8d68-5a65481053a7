import re
from django.core.management.base import BaseCommand
from academic.models import Course

class Command(BaseCommand):
    help = 'Cleans the recommended_semester field in the Course model.'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting semester cleaning process...'))
        
        courses_to_clean = Course.objects.filter(recommended_semester__icontains='-')
        
        cleaned_count = 0
        for course in courses_to_clean:
            original_value = str(course.recommended_semester)
            
            # 使用正则表达式提取范围中的第一个数字
            match = re.match(r'(\d+)', original_value)
            
            if match:
                new_value = int(match.group(1))
                self.stdout.write(f"Found course '{course.name}' with semester '{original_value}'. Converting to '{new_value}'.")
                course.recommended_semester = new_value
                course.save()
                cleaned_count += 1
            else:
                self.stdout.write(self.style.WARNING(f"Could not parse semester for course '{course.name}': '{original_value}'"))

        if cleaned_count > 0:
            self.stdout.write(self.style.SUCCESS(f'Successfully cleaned {cleaned_count} courses.'))
        else:
            self.stdout.write(self.style.SUCCESS('No courses needed cleaning.')) 